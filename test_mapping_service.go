package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

// 直接测试映射服务的行为
func main() {
	// 数据库连接
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi?sslmode=disable")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	ctx := context.Background()

	fmt.Println("🔍 测试快递公司映射服务对禁用状态的响应")
	fmt.Println("====================================================")

	// 测试供应商列表
	providers := []string{"kuaidiniao", "cainiao", "kuaidi100"}
	expressCode := "STO" // 申通快递

	// 1. 测试申通快递启用状态
	fmt.Println("\n📋 第一阶段：申通快递启用状态")
	fmt.Println("当前申通快递状态：启用")

	for _, provider := range providers {
		result := testMappingQuery(db, ctx, provider, expressCode)
		fmt.Printf("供应商 %s: %s\n", provider, result)
	}

	// 2. 禁用申通快递
	fmt.Println("\n🚫 禁用申通快递...")
	_, err = db.Exec("UPDATE express_companies SET is_active = false WHERE code = 'STO'")
	if err != nil {
		log.Fatal("禁用申通快递失败:", err)
	}
	fmt.Println("✅ 申通快递已禁用")

	// 3. 测试申通快递禁用状态
	fmt.Println("\n📋 第二阶段：申通快递禁用状态")
	fmt.Println("当前申通快递状态：禁用")

	for _, provider := range providers {
		result := testMappingQuery(db, ctx, provider, expressCode)
		fmt.Printf("供应商 %s: %s\n", provider, result)
	}

	// 4. 恢复申通快递
	fmt.Println("\n🔄 恢复申通快递启用状态...")
	_, err = db.Exec("UPDATE express_companies SET is_active = true WHERE code = 'STO'")
	if err != nil {
		log.Fatal("恢复申通快递失败:", err)
	}
	fmt.Println("✅ 申通快递已恢复启用")

	fmt.Println("\n🎯 测试完成！")
}

// 测试映射查询
func testMappingQuery(db *sql.DB, ctx context.Context, providerCode, companyCode string) string {
	// 模拟映射服务的查询逻辑
	query := `
		SELECT c.code, c.name, c.is_active, m.provider_company_code, m.is_supported
		FROM express_companies c
		LEFT JOIN express_company_provider_mappings m ON c.id = m.company_id
		LEFT JOIN express_providers p ON m.provider_id = p.id
		WHERE c.code = $1 AND p.code = $2
	`

	var companyCodeResult, companyName string
	var isActive, isSupported bool
	var providerCompanyCode string

	err := db.QueryRowContext(ctx, query, companyCode, providerCode).Scan(
		&companyCodeResult, &companyName, &isActive, &providerCompanyCode, &isSupported)

	if err != nil {
		if err == sql.ErrNoRows {
			return "❌ 映射关系不存在"
		}
		return fmt.Sprintf("❌ 查询失败: %v", err)
	}

	// 分析结果
	if !isActive {
		return "🚫 快递公司已禁用（应该被拒绝）"
	}

	if !isSupported {
		return "🚫 供应商不支持该快递公司"
	}

	return fmt.Sprintf("✅ 支持 (映射: %s -> %s)", companyCode, providerCompanyCode)
}
