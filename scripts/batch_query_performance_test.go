package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
)

// PerformanceTestConfig 性能测试配置
type PerformanceTestConfig struct {
	BaseURL         string        // API基础URL
	AuthToken       string        // 认证令牌
	BatchSizes      []int         // 批量大小测试列表
	ConcurrentUsers int           // 并发用户数
	TestDuration    time.Duration // 测试持续时间
	RequestTimeout  time.Duration // 请求超时时间
}

// TestResult 测试结果
type TestResult struct {
	BatchSize       int           `json:"batch_size"`
	TotalRequests   int           `json:"total_requests"`
	SuccessRequests int           `json:"success_requests"`
	FailedRequests  int           `json:"failed_requests"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	MinResponseTime time.Duration `json:"min_response_time"`
	MaxResponseTime time.Duration `json:"max_response_time"`
	ThroughputRPS   float64       `json:"throughput_rps"`
	ErrorRate       float64       `json:"error_rate"`
	Errors          []string      `json:"errors,omitempty"`
}

// BatchQueryPerformanceTester 批量查询性能测试器
type BatchQueryPerformanceTester struct {
	config *PerformanceTestConfig
	client *http.Client
}

// NewBatchQueryPerformanceTester 创建性能测试器
func NewBatchQueryPerformanceTester(config *PerformanceTestConfig) *BatchQueryPerformanceTester {
	return &BatchQueryPerformanceTester{
		config: config,
		client: &http.Client{
			Timeout: config.RequestTimeout,
		},
	}
}

// generateTrackingNumbers 生成测试用的运单号
func (t *BatchQueryPerformanceTester) generateTrackingNumbers(count int) []string {
	trackingNos := make([]string, count)
	prefixes := []string{"SF", "YT", "ZTO", "STO", "YD", "HTKY", "YZPY", "JD", "DBL"}

	for i := 0; i < count; i++ {
		prefix := prefixes[i%len(prefixes)]
		trackingNos[i] = fmt.Sprintf("%s%010d", prefix, 1000000000+i)
	}

	return trackingNos
}

// performBatchQuery 执行批量查询请求
func (t *BatchQueryPerformanceTester) performBatchQuery(trackingNos []string) (*TestResult, error) {
	start := time.Now()

	// 构建查询参数
	trackingNosStr := strings.Join(trackingNos, ",")
	url := fmt.Sprintf("%s/api/v1/express/orders?tracking_nos=%s&batch_mode=true",
		t.config.BaseURL, trackingNosStr)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置认证头
	req.Header.Set("Authorization", "Bearer "+t.config.AuthToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	duration := time.Since(start)

	// 解析响应
	var response model.OrderListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if !response.Success {
		return nil, fmt.Errorf("API返回错误: %s", response.Message)
	}

	// 验证批量查询统计信息
	if response.Data.BatchStats == nil {
		return nil, fmt.Errorf("缺少批量查询统计信息")
	}

	result := &TestResult{
		BatchSize:       len(trackingNos),
		TotalRequests:   1,
		SuccessRequests: 1,
		FailedRequests:  0,
		AvgResponseTime: duration,
		MinResponseTime: duration,
		MaxResponseTime: duration,
		ThroughputRPS:   1.0 / duration.Seconds(),
		ErrorRate:       0.0,
	}

	return result, nil
}

// runConcurrentTest 运行并发测试
func (t *BatchQueryPerformanceTester) runConcurrentTest(batchSize int) *TestResult {
	var wg sync.WaitGroup
	var mu sync.Mutex

	results := make([]*TestResult, 0)
	errors := make([]string, 0)

	// 生成测试数据
	trackingNos := t.generateTrackingNumbers(batchSize)

	// 启动并发测试
	for i := 0; i < t.config.ConcurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()

			startTime := time.Now()
			for time.Since(startTime) < t.config.TestDuration {
				result, err := t.performBatchQuery(trackingNos)

				mu.Lock()
				if err != nil {
					errors = append(errors, fmt.Sprintf("用户%d: %v", userID, err))
				} else {
					results = append(results, result)
				}
				mu.Unlock()

				// 短暂休息，避免过度压测
				time.Sleep(100 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()

	// 汇总结果
	return t.aggregateResults(batchSize, results, errors)
}

// aggregateResults 汇总测试结果
func (t *BatchQueryPerformanceTester) aggregateResults(batchSize int, results []*TestResult, errors []string) *TestResult {
	if len(results) == 0 {
		return &TestResult{
			BatchSize:       batchSize,
			TotalRequests:   len(errors),
			SuccessRequests: 0,
			FailedRequests:  len(errors),
			ErrorRate:       100.0,
			Errors:          errors,
		}
	}

	totalRequests := len(results) + len(errors)
	successRequests := len(results)
	failedRequests := len(errors)

	var totalResponseTime time.Duration
	minResponseTime := results[0].AvgResponseTime
	maxResponseTime := results[0].AvgResponseTime

	for _, result := range results {
		totalResponseTime += result.AvgResponseTime
		if result.AvgResponseTime < minResponseTime {
			minResponseTime = result.AvgResponseTime
		}
		if result.AvgResponseTime > maxResponseTime {
			maxResponseTime = result.AvgResponseTime
		}
	}

	avgResponseTime := totalResponseTime / time.Duration(len(results))
	throughputRPS := float64(successRequests) / t.config.TestDuration.Seconds()
	errorRate := float64(failedRequests) / float64(totalRequests) * 100

	return &TestResult{
		BatchSize:       batchSize,
		TotalRequests:   totalRequests,
		SuccessRequests: successRequests,
		FailedRequests:  failedRequests,
		AvgResponseTime: avgResponseTime,
		MinResponseTime: minResponseTime,
		MaxResponseTime: maxResponseTime,
		ThroughputRPS:   throughputRPS,
		ErrorRate:       errorRate,
		Errors:          errors,
	}
}

// RunPerformanceTest 运行性能测试
func (t *BatchQueryPerformanceTester) RunPerformanceTest() []*TestResult {
	fmt.Println("🚀 开始批量查询性能测试...")
	fmt.Printf("配置: 并发用户=%d, 测试时长=%v, 请求超时=%v\n",
		t.config.ConcurrentUsers, t.config.TestDuration, t.config.RequestTimeout)

	results := make([]*TestResult, 0)

	for _, batchSize := range t.config.BatchSizes {
		fmt.Printf("\n📊 测试批量大小: %d\n", batchSize)

		result := t.runConcurrentTest(batchSize)
		results = append(results, result)

		// 打印即时结果
		fmt.Printf("  总请求数: %d\n", result.TotalRequests)
		fmt.Printf("  成功请求: %d\n", result.SuccessRequests)
		fmt.Printf("  失败请求: %d\n", result.FailedRequests)
		fmt.Printf("  平均响应时间: %v\n", result.AvgResponseTime)
		fmt.Printf("  最小响应时间: %v\n", result.MinResponseTime)
		fmt.Printf("  最大响应时间: %v\n", result.MaxResponseTime)
		fmt.Printf("  吞吐量: %.2f RPS\n", result.ThroughputRPS)
		fmt.Printf("  错误率: %.2f%%\n", result.ErrorRate)

		if len(result.Errors) > 0 {
			fmt.Printf("  错误示例: %s\n", result.Errors[0])
		}

		// 测试间隔
		time.Sleep(2 * time.Second)
	}

	return results
}

// generateReport 生成性能测试报告
func (t *BatchQueryPerformanceTester) generateReport(results []*TestResult) {
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📈 批量查询性能测试报告")
	fmt.Println(strings.Repeat("=", 80))

	fmt.Printf("%-10s %-12s %-12s %-15s %-15s %-12s %-10s\n",
		"批量大小", "总请求数", "成功请求", "平均响应时间", "最大响应时间", "吞吐量(RPS)", "错误率(%)")
	fmt.Println(strings.Repeat("-", 80))

	for _, result := range results {
		fmt.Printf("%-10d %-12d %-12d %-15v %-15v %-12.2f %-10.2f\n",
			result.BatchSize,
			result.TotalRequests,
			result.SuccessRequests,
			result.AvgResponseTime,
			result.MaxResponseTime,
			result.ThroughputRPS,
			result.ErrorRate)
	}

	fmt.Println(strings.Repeat("=", 80))

	// 性能建议
	t.generatePerformanceRecommendations(results)
}

// generatePerformanceRecommendations 生成性能建议
func (t *BatchQueryPerformanceTester) generatePerformanceRecommendations(results []*TestResult) {
	fmt.Println("\n💡 性能优化建议:")

	for _, result := range results {
		if result.ErrorRate > 5.0 {
			fmt.Printf("⚠️  批量大小 %d: 错误率过高(%.2f%%)，建议减少批量大小或增加服务器资源\n",
				result.BatchSize, result.ErrorRate)
		}

		if result.AvgResponseTime > 2*time.Second {
			fmt.Printf("⚠️  批量大小 %d: 响应时间过长(%v)，建议优化数据库查询或增加索引\n",
				result.BatchSize, result.AvgResponseTime)
		}

		if result.ThroughputRPS < 10 {
			fmt.Printf("⚠️  批量大小 %d: 吞吐量过低(%.2f RPS)，建议优化服务器性能\n",
				result.BatchSize, result.ThroughputRPS)
		}
	}

	// 找出最佳批量大小
	var bestResult *TestResult
	bestScore := 0.0

	for _, result := range results {
		if result.ErrorRate < 1.0 && result.AvgResponseTime < time.Second {
			score := result.ThroughputRPS * float64(result.BatchSize) / result.AvgResponseTime.Seconds()
			if score > bestScore {
				bestScore = score
				bestResult = result
			}
		}
	}

	if bestResult != nil {
		fmt.Printf("✅ 推荐批量大小: %d (响应时间: %v, 吞吐量: %.2f RPS, 错误率: %.2f%%)\n",
			bestResult.BatchSize, bestResult.AvgResponseTime, bestResult.ThroughputRPS, bestResult.ErrorRate)
	}
}

func main() {
	// 配置性能测试参数
	config := &PerformanceTestConfig{
		BaseURL:         "http://localhost:8080",  // 根据实际情况修改
		AuthToken:       "your-test-token",        // 根据实际情况修改
		BatchSizes:      []int{5, 10, 20, 30, 50}, // 测试不同的批量大小
		ConcurrentUsers: 10,                       // 并发用户数
		TestDuration:    30 * time.Second,         // 每个批量大小测试30秒
		RequestTimeout:  10 * time.Second,         // 请求超时时间
	}

	// 创建测试器
	tester := NewBatchQueryPerformanceTester(config)

	// 运行性能测试
	results := tester.RunPerformanceTest()

	// 生成报告
	tester.generateReport(results)

	// 保存结果到JSON文件
	if data, err := json.MarshalIndent(results, "", "  "); err == nil {
		timestamp := time.Now().Format("20060102_150405")
		filename := fmt.Sprintf("batch_query_performance_report_%s.json", timestamp)
		if err := os.WriteFile(filename, data, 0644); err == nil {
			fmt.Printf("\n📄 详细报告已保存到: %s\n", filename)
		}
	}
}
