package main

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// 模拟管理员充值请求
type AdminDepositRequest struct {
	UserID      string          `json:"user_id"`
	Amount      decimal.Decimal `json:"amount"`
	Reason      string          `json:"reason"`
	Description string          `json:"description"`
	AdminID     string          `json:"admin_id"`
}

// 模拟配置服务
type MockConfigService struct {
	configs map[string]string
}

func NewMockConfigService() *MockConfigService {
	return &MockConfigService{
		configs: map[string]string{
			"admin_balance.min_balance_amount": "0.01",
			"admin_balance.max_single_amount":  "10000.00", // 这个配置现在被忽略了
		},
	}
}

func (s *MockConfigService) GetConfigWithDefault(key, defaultValue string) string {
	if value, exists := s.configs[key]; exists {
		return value
	}
	return defaultValue
}

// 模拟管理员余额服务的验证逻辑
type MockAdminBalanceService struct {
	configService *MockConfigService
	logger        *zap.Logger
}

func NewMockAdminBalanceService() *MockAdminBalanceService {
	logger, _ := zap.NewDevelopment()
	return &MockAdminBalanceService{
		configService: NewMockConfigService(),
		logger:        logger,
	}
}

// 🔥 修改后的验证逻辑：管理员充值不限制最大金额
func (s *MockAdminBalanceService) validateDepositRequest(req *AdminDepositRequest) error {
	if req.UserID == "" {
		return fmt.Errorf("用户ID不能为空")
	}

	// 从配置获取最小金额限制
	minAmount := s.configService.GetConfigWithDefault("admin_balance.min_balance_amount", "0.01")
	minAmountDecimal, _ := decimal.NewFromString(minAmount)

	// 🔥 修改：管理员手动充值不限制最大金额，只检查最小金额
	if req.Amount.LessThan(minAmountDecimal) {
		return fmt.Errorf("充值金额不能小于%s", minAmount)
	}

	// 🔥 移除最大金额限制检查，管理员充值不受限制
	// 只保留基本的业务逻辑验证：金额必须为正数
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("充值金额必须大于0")
	}

	if req.Reason == "" {
		return fmt.Errorf("充值原因不能为空")
	}
	if req.AdminID == "" {
		return fmt.Errorf("管理员ID不能为空")
	}

	s.logger.Info("管理员充值验证通过",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("reason", req.Reason))

	return nil
}

// 模拟充值操作
func (s *MockAdminBalanceService) ManualDeposit(ctx context.Context, req *AdminDepositRequest) error {
	// 验证请求
	if err := s.validateDepositRequest(req); err != nil {
		return err
	}

	// 模拟充值成功
	s.logger.Info("管理员充值成功",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()))

	return nil
}

// 测试不同金额的充值
func testDepositAmount(service *MockAdminBalanceService, amount decimal.Decimal, description string) {
	fmt.Printf("\n=== 测试 %s (%.2f 元) ===\n", description, amount.InexactFloat64())

	req := &AdminDepositRequest{
		UserID:      "test_user_001",
		Amount:      amount,
		Reason:      "测试充值",
		Description: description,
		AdminID:     "admin_001",
	}

	ctx := context.Background()
	err := service.ManualDeposit(ctx, req)

	if err != nil {
		fmt.Printf("❌ 充值失败: %v\n", err)
	} else {
		fmt.Printf("✅ 充值成功: %.2f 元\n", amount.InexactFloat64())
	}
}

func main() {
	fmt.Println("🔧 管理员充值限额修改测试")
	fmt.Println("========================================")

	service := NewMockAdminBalanceService()

	// 测试各种金额
	testCases := []struct {
		amount      decimal.Decimal
		description string
	}{
		{decimal.NewFromFloat(0.01), "最小金额"},
		{decimal.NewFromFloat(100.0), "普通金额"},
		{decimal.NewFromFloat(1000.0), "较大金额"},
		{decimal.NewFromFloat(10000.0), "原限额金额"},
		{decimal.NewFromFloat(50000.0), "超过原限额"},
		{decimal.NewFromFloat(100000.0), "大额充值"},
		{decimal.NewFromFloat(1000000.0), "超大额充值"},
		{decimal.NewFromFloat(0.001), "小于最小金额"},
		{decimal.Zero, "零金额"},
		{decimal.NewFromFloat(-100.0), "负数金额"},
	}

	for _, tc := range testCases {
		testDepositAmount(service, tc.amount, tc.description)
	}

	fmt.Println("\n========================================")
	fmt.Println("🎉 修改效果总结:")
	fmt.Println("1. ✅ 移除了最大金额限制 (原来10000元限制)")
	fmt.Println("2. ✅ 保留了最小金额限制 (0.01元)")
	fmt.Println("3. ✅ 保留了基本验证 (正数、非空等)")
	fmt.Println("4. ✅ 管理员现在可以充值任意大额金额")
	fmt.Println("5. ✅ 增加了详细的日志记录")
	fmt.Println("\n🚀 管理员充值限额修改完成！现在不限制最大充值金额！")
}
