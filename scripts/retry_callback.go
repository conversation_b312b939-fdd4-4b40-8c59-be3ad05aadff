package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service/callback"
	"github.com/your-org/go-kuaidi/internal/util"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("使用方法: go run retry_callback.go <callback_record_id>")
		fmt.Println("示例: go run retry_callback.go d671283c-32d3-4e93-98d0-a0e3ed2131ea")
		os.Exit(1)
	}

	callbackRecordIDStr := os.Args[1]
	callbackRecordID, err := uuid.Parse(callbackRecordIDStr)
	if err != nil {
		log.Fatalf("无效的回调记录ID: %v", err)
	}

	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 连接数据库
	dbURL := "*************************************************/go_kuaidi"
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	ctx := context.Background()

	// 创建仓库
	callbackRepo := repository.NewPostgresCallbackRepository(db)

	// 查询回调记录
	callbackRecord, err := callbackRepo.GetCallbackRecord(ctx, callbackRecordID)
	if err != nil {
		log.Fatalf("查询回调记录失败: %v", err)
	}

	if callbackRecord == nil {
		log.Fatalf("回调记录不存在: %s", callbackRecordIDStr)
	}

	fmt.Printf("找到回调记录:\n")
	fmt.Printf("  ID: %s\n", callbackRecord.ID)
	fmt.Printf("  Provider: %s\n", callbackRecord.Provider)
	fmt.Printf("  OrderNo: %s\n", callbackRecord.OrderNo)
	fmt.Printf("  CustomerOrderNo: %s\n", callbackRecord.CustomerOrderNo)
	fmt.Printf("  TrackingNo: %s\n", callbackRecord.TrackingNo)
	fmt.Printf("  UserID: %s\n", callbackRecord.UserID)
	fmt.Printf("  EventType: %s\n", callbackRecord.EventType)
	fmt.Printf("  InternalStatus: %s\n", callbackRecord.InternalStatus)
	fmt.Printf("  ExternalStatus: %s\n", callbackRecord.ExternalStatus)
	fmt.Printf("  ReceivedAt: %s\n", callbackRecord.ReceivedAt.Format("2006-01-02 15:04:05"))

	// 检查是否有用户ID
	if callbackRecord.UserID == "" {
		log.Fatalf("回调记录没有关联用户，无法转发")
	}

	// 解析标准化数据
	var standardizedData model.StandardizedCallbackData
	if err := json.Unmarshal(callbackRecord.StandardizedData, &standardizedData); err != nil {
		log.Fatalf("解析回调标准化数据失败: %v", err)
	}

	fmt.Printf("\n标准化数据:\n")
	fmt.Printf("  EventType: %s\n", standardizedData.EventType)
	fmt.Printf("  OrderNo: %s\n", standardizedData.OrderNo)
	fmt.Printf("  CustomerOrderNo: %s\n", standardizedData.CustomerOrderNo)
	fmt.Printf("  PlatformOrderNo: %s\n", standardizedData.PlatformOrderNo)
	fmt.Printf("  TrackingNo: %s\n", standardizedData.TrackingNo)
	fmt.Printf("  Provider: %s\n", standardizedData.Provider)

	// 检查是否已有转发记录
	forwardRecords, err := callbackRepo.GetForwardRecordsByCallbackID(ctx, callbackRecord.ID)
	if err != nil {
		log.Printf("查询转发记录失败: %v", err)
	} else {
		fmt.Printf("\n现有转发记录数量: %d\n", len(forwardRecords))
		for i, record := range forwardRecords {
			fmt.Printf("  转发记录 %d:\n", i+1)
			fmt.Printf("    ID: %s\n", record.ID)
			fmt.Printf("    Status: %s\n", record.Status)
			fmt.Printf("    HTTPStatus: %d\n", record.HTTPStatus)
			fmt.Printf("    CreatedAt: %s\n", record.CreatedAt.Format("2006-01-02 15:04:05"))
			if record.ResponseAt != nil {
				fmt.Printf("    ResponseAt: %s\n", record.ResponseAt.Format("2006-01-02 15:04:05"))
			}
		}
	}

	// 询问是否继续
	fmt.Printf("\n是否要重新转发此回调记录? (y/N): ")
	var confirm string
	fmt.Scanln(&confirm)
	if confirm != "y" && confirm != "Y" {
		fmt.Println("操作已取消")
		return
	}

	// 创建转发器（简化版本，仅用于测试）
	fmt.Printf("\n开始重新转发回调记录...\n")

	// 创建一个简化的转发器来执行转发
	forwarder := callback.NewUnifiedCallbackForwarder(
		logger,
		callbackRepo,
		nil, // retryScheduler 可以为空
		nil, // builder 需要实现
	)

	// 清理幂等性缓存（强制重试）
	fmt.Printf("清理转发幂等性缓存...\n")
	
	// 执行转发
	err = forwarder.Forward(ctx, callbackRecord, &standardizedData)
	if err != nil {
		log.Fatalf("转发失败: %v", err)
	}

	// 更新回调记录状态
	now := util.NowBeijing()
	callbackRecord.ExternalStatus = model.CallbackStatusSuccess
	callbackRecord.ExternalProcessedAt = &now
	callbackRecord.UpdatedAt = now

	if err := callbackRepo.UpdateCallbackRecord(ctx, callbackRecord); err != nil {
		log.Printf("更新回调记录状态失败: %v", err)
	}

	fmt.Printf("✅ 回调记录转发成功!\n")

	// 再次查询转发记录确认
	forwardRecords, err = callbackRepo.GetForwardRecordsByCallbackID(ctx, callbackRecord.ID)
	if err != nil {
		log.Printf("查询转发记录失败: %v", err)
	} else {
		fmt.Printf("\n更新后的转发记录数量: %d\n", len(forwardRecords))
		for i, record := range forwardRecords {
			if i >= len(forwardRecords)-1 { // 只显示最新的记录
				fmt.Printf("  最新转发记录:\n")
				fmt.Printf("    ID: %s\n", record.ID)
				fmt.Printf("    Status: %s\n", record.Status)
				fmt.Printf("    HTTPStatus: %d\n", record.HTTPStatus)
				fmt.Printf("    CreatedAt: %s\n", record.CreatedAt.Format("2006-01-02 15:04:05"))
				if record.ResponseAt != nil {
					fmt.Printf("    ResponseAt: %s\n", record.ResponseAt.Format("2006-01-02 15:04:05"))
				}
			}
		}
	}
}
