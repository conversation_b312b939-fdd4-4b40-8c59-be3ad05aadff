# 🔥 代码时区逻辑清理计划

## 第四阶段：代码修改清单

### 4.1 删除时区管理器相关代码

**删除文件：**
- `internal/timezone/manager.go` - 完整删除
- `user-frontend/src/utils/timezone.js` - 完整删除  
- `user-frontend/src/utils/timezone.d.ts` - 完整删除

**修改文件：**
- 所有导入 `timezone` 包的Go文件
- 所有导入 `timezone.js` 的前端文件

### 4.2 后端代码修改

**核心修改点：**

1. **删除时区转换逻辑**
   - `internal/repository/*.go` - 删除所有 `ToBeijingTime()` 调用
   - `internal/service/*.go` - 删除时区相关导入和调用
   - `internal/handler/*.go` - 删除时区处理逻辑

2. **简化时间处理**
   ```go
   // 删除前：
   createdAt := timezone.GetGlobalTimeZone().ToBeijingTime(order.CreatedAt)
   
   // 删除后：
   createdAt := order.CreatedAt  // 数据库已经是北京时间
   ```

3. **删除时区初始化**
   - `cmd/main.go` - 删除 `timezone.InitGlobalTimeZone()`
   - `internal/app/app.go` - 删除时区管理器初始化

### 4.3 前端代码修改

**核心修改点：**

1. **删除时区格式化函数**
   ```javascript
   // 删除前：
   import { formatOrderDateTime } from '@/utils/timezone'
   const time = formatOrderDateTime(order.created_at)
   
   // 删除后：
   const time = new Date(order.created_at).toLocaleString('zh-CN')
   ```

2. **简化时间显示**
   - 删除所有 `timeZone: 'Asia/Shanghai'` 参数
   - 使用标准的 `toLocaleString()` 方法

### 4.4 数据库连接配置

**修改数据库连接字符串：**
```go
// 删除前：
dsn := "postgresql://user:pass@host:port/db?sslmode=disable"

// 删除后：
dsn := "postgresql://user:pass@host:port/db?sslmode=disable&timezone=Asia/Shanghai"
```

### 4.5 API文档更新

**修改点：**
- 所有时间字段的文档说明
- API响应示例中的时间格式
- 前端集成文档

### 4.6 测试用例更新

**修改点：**
- 时间相关的单元测试
- 集成测试中的时间断言
- 前端时间显示测试

## 预计工作量

**总工作量：2-3小时**

- 数据库迁移：30分钟
- 后端代码清理：60分钟  
- 前端代码清理：30分钟
- 测试验证：60分钟

## 风险控制

1. **数据备份**：迁移前完整备份
2. **分步执行**：可以分阶段回滚
3. **验证机制**：每步都有验证SQL
4. **回滚方案**：准备完整回滚脚本
