#!/bin/bash

# 🔥 时区迁移执行脚本
# 用途：自动化执行UTC到北京时间的完整迁移
# 执行时间：预计10-15分钟

set -e  # 遇到错误立即退出

# 配置
DB_URL="***********************************************************"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/migration-$(date +%Y%m%d_%H%M%S).log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# 检查数据库连接
check_database() {
    log "检查数据库连接..."
    if psql "$DB_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        success "数据库连接正常"
    else
        error "数据库连接失败，请检查连接字符串"
        exit 1
    fi
}

# 创建备份
create_backup() {
    log "创建数据备份..."
    BACKUP_FILE="$SCRIPT_DIR/backup-$(date +%Y%m%d_%H%M%S).sql"
    
    # 备份核心表
    pg_dump "$DB_URL" \
        --table=order_records \
        --table=balance_transactions \
        --table=order_status_history \
        --table=work_orders \
        --table=callback_forward_records \
        --table=users \
        --table=user_balances \
        --data-only \
        --file="$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        success "数据备份完成: $BACKUP_FILE"
    else
        error "数据备份失败"
        exit 1
    fi
}

# 执行SQL脚本
execute_sql() {
    local script_file=$1
    local description=$2
    
    log "执行 $description..."
    if psql "$DB_URL" -f "$script_file" >> "$LOG_FILE" 2>&1; then
        success "$description 执行成功"
    else
        error "$description 执行失败，查看日志: $LOG_FILE"
        exit 1
    fi
}

# 验证迁移结果
validate_migration() {
    log "验证迁移结果..."
    
    # 执行验证查询
    VALIDATION_RESULT=$(psql "$DB_URL" -t -c "
        SELECT COUNT(*) 
        FROM timezone_migration_log 
        WHERE status = 'completed' OR status IS NULL;
    ")
    
    if [ "$VALIDATION_RESULT" -gt 0 ]; then
        success "迁移验证通过"
        
        # 显示详细验证结果
        psql "$DB_URL" -c "
            SELECT 
                table_name,
                operation,
                records_affected,
                start_time,
                status
            FROM timezone_migration_log 
            ORDER BY start_time DESC 
            LIMIT 10;
        " | tee -a "$LOG_FILE"
    else
        error "迁移验证失败"
        exit 1
    fi
}

# 主执行流程
main() {
    log "🔥 开始时区迁移：UTC → 北京时间"
    log "日志文件: $LOG_FILE"
    
    # 确认执行
    echo -e "${YELLOW}警告：此操作将修改数据库中的所有时间字段${NC}"
    echo -e "${YELLOW}请确保已经停止所有应用服务${NC}"
    read -p "确认继续执行迁移？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "用户取消迁移"
        exit 0
    fi
    
    # 执行步骤
    check_database
    create_backup
    
    execute_sql "$SCRIPT_DIR/01-database-timezone-setup.sql" "数据库时区设置"
    execute_sql "$SCRIPT_DIR/02-core-tables-migration.sql" "核心表数据迁移"
    execute_sql "$SCRIPT_DIR/03-config-tables-migration.sql" "配置表数据迁移"
    
    validate_migration
    
    success "🎉 时区迁移完成！"
    log "下一步：执行代码清理（参考 04-code-cleanup-plan.md）"
    
    # 显示迁移后的示例数据
    log "迁移后的示例数据："
    psql "$DB_URL" -c "
        SELECT 
            customer_order_no,
            created_at,
            updated_at
        FROM order_records 
        WHERE customer_order_no = '8751424_157';
    " | tee -a "$LOG_FILE"
}

# 执行主函数
main "$@"
