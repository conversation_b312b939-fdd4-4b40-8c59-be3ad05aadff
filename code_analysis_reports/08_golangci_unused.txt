专门检查未使用代码:
level=warning msg="[runner] The linter 'deadcode' is deprecated (since v1.49.0) due to: The owner seems to have abandoned the linter. Replaced by unused."
level=warning msg="[runner] The linter 'varcheck' is deprecated (since v1.49.0) due to: The owner seems to have abandoned the linter. Replaced by unused."
level=warning msg="[runner] The linter 'structcheck' is deprecated (since v1.49.0) due to: The owner seems to have abandoned the linter. Replaced by unused."
api/handler/address_handler.go:1: : # github.com/your-org/go-kuaidi/api/handler [github.com/your-org/go-kuaidi/api/handler.test]
api/handler/order_handler_batch_test.go:4:2: "bytes" imported and not used
api/handler/order_handler_batch_test.go:9:2: "net/url" imported and not used
api/handler/order_handler_batch_test.go:43:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:109:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:162:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:206:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal (typecheck)
package handler
api/routes/admin_raw_callback_routes.go:1: : # github.com/your-org/go-kuaidi/api/routes
api/routes/admin_raw_callback_routes.go:15:39: undefined: middleware.AdminAuthMiddleware (typecheck)
package routes
cmd/stress_test/main.go:11:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/adapter/kuaidiniao.go:110:23: undefined: validator (typecheck)
		validator:          validator.New(),
		                    ^
internal/adapter/kuaidiniao_types.go:28:22: undefined: validator (typecheck)
	validator          *validator.Validate
	                    ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:25:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, workOrder)
	          ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:30:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, id)
	          ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:35:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, provider, providerWorkOrderID)
	          ^
internal/auth/controller.go:138:23: claims.ID undefined (type *TokenClaims has no field or method ID) (typecheck)
		TokenID:     claims.ID,
		                    ^
internal/auth/middleware.go:56:13: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
		if claims.Subject != "" {
		          ^
internal/auth/middleware.go:58:28: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
			c.Set("user_id", claims.Subject)
			                        ^
internal/auth/middleware.go:59:27: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
			c.Set("userID", claims.Subject) // 兼容其他中间件
			                       ^
internal/auth/service.go:30:2: undefined: jwt (typecheck)
	jwt.RegisteredClaims
	^
internal/auth/service.go:42:16: undefined: jwt (typecheck)
	signingMethod jwt.SigningMethod
	              ^
internal/auth/service.go:61:18: undefined: jwt (typecheck)
		signingMethod: jwt.SigningMethodRS256,
		               ^
internal/benchmark/performance_benchmark.go:8:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/config/migration.go:175:19: undefined: yaml (typecheck)
	yamlData, err := yaml.Marshal(unifiedConfig)
	                 ^
internal/logging/manager.go:189:13: undefined: lumberjack (typecheck)
	writer := &lumberjack.Logger{
	           ^
internal/security/nonce_manager.go:83:15: undefined: redis (typecheck)
	redisClient *redis.Client
	             ^
internal/security/nonce_manager.go:90:55: undefined: redis (typecheck)
func NewNonceManager(config NonceConfig, redisClient *redis.Client, logger *zap.Logger) NonceManager {
                                                      ^
internal/security/rate_limit.go:75:15: undefined: redis (typecheck)
	redisClient *redis.Client
	             ^
internal/service/balance_monitoring_service.go:6:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/service/balance_service_test.go:24:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, userID)
	          ^
internal/service/balance_service_test.go:32:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, balance)
	          ^
internal/service/balance_service_test.go:37:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, userID, newBalance, frozenBalance, version)
	          ^
internal/service/balance_service_test.go:103:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:119:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/balance_service_test.go:146:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:163:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/balance_service_test.go:188:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:201:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/billing_difference_checker_test.go:21:12: m.Called undefined (type *MockOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, order)
	          ^
internal/service/billing_difference_checker_test.go:26:12: m.Called undefined (type *MockOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, order)
	          ^
internal/service/billing_difference_checker_test.go:31:12: m.Called undefined (type *MockOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, id)
	          ^
internal/service/billing_difference_checker_test.go:56:12: m.Called undefined (type *MockBalanceService has no field or method Called) (typecheck)
	args := m.Called(ctx, userID, orderNo, customerOrderNo)
	          ^
internal/service/billing_difference_checker_test.go:66:12: m.Called undefined (type *MockBillingService has no field or method Called) (typecheck)
	args := m.Called(ctx, orderNo, actualFee, reason)
	          ^
internal/service/enhanced_balance_manager.go:94:12: undefined: yaml (typecheck)
	if err := yaml.Unmarshal(configData, &config); err != nil {
	          ^
internal/service/order_balance_integration_test.go:22:12: m.Called undefined (type *MockOrderService has no field or method Called) (typecheck)
	args := m.Called(ctx, req)
	          ^
internal/service/order_balance_integration_test.go:87:20: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
			mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
			                ^
internal/service/order_balance_integration_test.go:107:20: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
			mockBalanceRepo.AssertExpectations(t)
			                ^
internal/service/order_balance_integration_test.go:135:18: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil).Times(5)
	                ^
internal/service/order_balance_integration_test.go:165:18: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockBalanceRepo.AssertExpectations(t)
	                ^
internal/service/order_balance_integration_test.go:183:18: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户余额不存在"))
	                ^
internal/service/order_balance_integration_test.go:194:18: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockBalanceRepo.AssertExpectations(t)
	                ^
internal/user/validation_utils.go:7:2: could not import unicode (-: could not load export data: internal error in importing "unicode" (unsupported version: 2); please report an issue) (typecheck)
	"unicode"
	^
internal/util/json_optimizer.go:21:10: undefined: jsoniter (typecheck)
	jsonAPI jsoniter.API
	        ^
internal/util/query_validator.go:7:2: could not import unicode/utf8 (-: could not load export data: internal error in importing "unicode/utf8" (unsupported version: 2); please report an issue) (typecheck)
	"unicode/utf8"
	^
internal/validator/input_validator.go:8:2: could not import unicode (-: could not load export data: internal error in importing "unicode" (unsupported version: 2); please report an issue) (typecheck)
	"unicode"
	^
internal/validator/input_validator.go:9:2: could not import unicode/utf8 (-: could not load export data: internal error in importing "unicode/utf8" (unsupported version: 2); please report an issue) (typecheck)
	"unicode/utf8"
	^
test_express_disable_fix.go:1: : # github.com/your-org/go-kuaidi
./test_mapping_service.go:13:6: main redeclared in this block
	./test_express_disable_fix.go:17:6: other declaration of main
./test_express_disable_fix.go:4:2: "context" imported and not used
./test_express_disable_fix.go:38:14: invalid operation: "=" * 60 (mismatched types untyped string and untyped int) (typecheck)
package main
