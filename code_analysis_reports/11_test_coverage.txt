测试覆盖率分析:
# github.com/your-org/go-kuaidi/internal/service
internal/service/order_balance_integration_test.go:13:2: package go-kuaidi/internal/errors is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/errors)
FAIL	github.com/your-org/go-kuaidi/internal/service [setup failed]
# github.com/your-org/go-kuaidi
./test_mapping_service.go:13:6: main redeclared in this block
	./test_express_disable_fix.go:17:6: other declaration of main
./test_express_disable_fix.go:4:2: "context" imported and not used
./test_express_disable_fix.go:38:14: invalid operation: "=" * 60 (mismatched types untyped string and untyped int)
FAIL	github.com/your-org/go-kuaidi [build failed]
# github.com/your-org/go-kuaidi/api/handler [github.com/your-org/go-kuaidi/api/handler.test]
api/handler/order_handler_batch_test.go:4:2: "bytes" imported and not used
api/handler/order_handler_batch_test.go:9:2: "net/url" imported and not used
api/handler/order_handler_batch_test.go:43:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:109:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:162:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:206:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
FAIL	github.com/your-org/go-kuaidi/api/handler [build failed]
	github.com/your-org/go-kuaidi/api/middleware		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/api/router		coverage: 0.0% of statements
# github.com/your-org/go-kuaidi/api/routes
api/routes/admin_raw_callback_routes.go:15:39: undefined: middleware.AdminAuthMiddleware
FAIL	github.com/your-org/go-kuaidi/api/routes [build failed]
	github.com/your-org/go-kuaidi/cmd		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/apiclient		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/billing-checker		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/config-tool		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/demo		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/genkeys		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/init-routes		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/init-weight-cache		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/manageclient		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/migrate		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/nonce-generator		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/security-check		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/simple_demo		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/standalone_demo		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/stress_test		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/test-kuaidiniao-adapter		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/cmd/workorder		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/docs		coverage: 0.0% of statements
--- FAIL: TestKuaidiNiaoAdapter_standardizeCity (0.00s)
    kuaidiniao_test.go:130: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/kuaidiniao_test.go:130
        	Error:      	Not equal: 
        	            	expected: "广州市"
        	            	actual  : "广州"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-广州市
        	            	+广州
        	Test:       	TestKuaidiNiaoAdapter_standardizeCity
        	Messages:   	Failed for input: 广州
    kuaidiniao_test.go:130: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/kuaidiniao_test.go:130
        	Error:      	Not equal: 
        	            	expected: "苏州市"
        	            	actual  : "苏州"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-苏州市
        	            	+苏州
        	Test:       	TestKuaidiNiaoAdapter_standardizeCity
        	Messages:   	Failed for input: 苏州
    kuaidiniao_test.go:130: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/kuaidiniao_test.go:130
        	Error:      	Not equal: 
        	            	expected: "延边州"
        	            	actual  : "延边市"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-延边州
        	            	+延边市
        	Test:       	TestKuaidiNiaoAdapter_standardizeCity
        	Messages:   	Failed for input: 延边
    kuaidiniao_test.go:130: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/kuaidiniao_test.go:130
        	Error:      	Not equal: 
        	            	expected: "阿拉善盟"
        	            	actual  : "阿拉善市"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-阿拉善盟
        	            	+阿拉善市
        	Test:       	TestKuaidiNiaoAdapter_standardizeCity
        	Messages:   	Failed for input: 阿拉善
{"level":"warn","ts":1754804691.7566001,"caller":"adapter/kuaidiniao.go:674","msg":"快递鸟：数据库仓库未初始化，使用默认抛比8000"}
{"level":"warn","ts":1754804691.756644,"caller":"adapter/kuaidiniao.go:674","msg":"快递鸟：数据库仓库未初始化，使用默认抛比8000"}
--- FAIL: TestKuaidiNiaoAdapter_calculateChargedWeight (0.00s)
    --- FAIL: TestKuaidiNiaoAdapter_calculateChargedWeight/Volume_weight_larger_than_actual (0.00s)
        kuaidiniao_test.go:192: 
            	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/kuaidiniao_test.go:192
            	Error:      	Not equal: 
            	            	expected: 10
            	            	actual  : 8
            	Test:       	TestKuaidiNiaoAdapter_calculateChargedWeight/Volume_weight_larger_than_actual
FAIL
coverage: 2.1% of statements
FAIL	github.com/your-org/go-kuaidi/internal/adapter	0.021s
--- FAIL: TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder (0.00s)
    kuaidiniao_adapter_test.go:195: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/workorder/kuaidiniao_adapter_test.go:195
        	Error:      	Received unexpected error:
        	            	快递鸟工单参数验证失败: 不支持的快递鸟工单类型: 1
        	Test:       	TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder
    kuaidiniao_adapter_test.go:196: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/workorder/kuaidiniao_adapter_test.go:196
        	Error:      	Expected value not to be nil.
        	Test:       	TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder
panic: runtime error: invalid memory address or nil pointer dereference [recovered]
	panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x0 pc=0x10bd33eea]

goroutine 19 [running]:
testing.tRunner.func1.2({0x10c0b72e0, 0x10ca853c0})
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1734 +0x21c
testing.tRunner.func1()
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1737 +0x35e
panic({0x10c0b72e0?, 0x10ca853c0?})
	/usr/local/Cellar/go/1.24.4/libexec/src/runtime/panic.go:792 +0x132
github.com/your-org/go-kuaidi/internal/adapter/workorder.TestKuaidiNiaoWorkOrderAdapter_CreateWorkOrder(0xc000102540)
	/Users/<USER>/Desktop/go-kuaidi-*********/internal/adapter/workorder/kuaidiniao_adapter_test.go:197 +0x40a
testing.tRunner(0xc000102540, 0x10c20c438)
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 1
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1851 +0x413
FAIL	github.com/your-org/go-kuaidi/internal/adapter/workorder	0.021s
	github.com/your-org/go-kuaidi/internal/api		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/auth		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/benchmark		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/cache		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/concurrent		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/config		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/constants		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/consumer		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/database		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/errors		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/express		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/handler		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/http		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/infrastructure/database		coverage: 0.0% of statements
?   	github.com/your-org/go-kuaidi/internal/interfaces	[no test files]
	github.com/your-org/go-kuaidi/internal/logging		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/memory		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/middleware		coverage: 0.0% of statements
# github.com/your-org/go-kuaidi/scripts [github.com/your-org/go-kuaidi/scripts.test]
scripts/test_admin_deposit_limit.go:130:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_check_integration.go:138:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_fix.go:122:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_service_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_service_fix.go:25:6: MockBalanceService redeclared in this block
	scripts/test_balance_fix.go:25:6: other declaration of MockBalanceService
scripts/test_balance_service_fix.go:30:6: NewMockBalanceService redeclared in this block
	scripts/test_balance_fix.go:30:6: other declaration of NewMockBalanceService
scripts/test_balance_service_fix.go:114:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:14:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_prometheus_fix.go:157:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:157:6: too many errors
ok  	github.com/your-org/go-kuaidi/internal/model	0.020s	coverage: 12.7% of statements
	github.com/your-org/go-kuaidi/internal/pool		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/region		coverage: 0.0% of statements
ok  	github.com/your-org/go-kuaidi/internal/repository	0.023s	coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/security		coverage: 0.0% of statements
--- FAIL: TestCainiaoCallbackAdapter_ParseCallback (0.00s)
    --- FAIL: TestCainiaoCallbackAdapter_ParseCallback/无效的JSON格式 (0.00s)
        cainiao_adapter_test.go:146: 
            	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/cainiao_adapter_test.go:146
            	Error:      	"解析菜鸟裹裹JSON回调数据失败: invalid character 'i' looking for beginning of object key string" does not contain "解析菜鸟裹裹外层回调数据失败"
            	Test:       	TestCainiaoCallbackAdapter_ParseCallback/无效的JSON格式
--- FAIL: TestCainiaoCallbackAdapter_BuildResponse (0.00s)
    cainiao_adapter_test.go:205: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/cainiao_adapter_test.go:205
        	Error:      	Not equal: 
        	            	expected: bool(true)
        	            	actual  : <nil>(<nil>)
        	Test:       	TestCainiaoCallbackAdapter_BuildResponse
    cainiao_adapter_test.go:206: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/cainiao_adapter_test.go:206
        	Error:      	Not equal: 
        	            	expected: string("接收成功")
        	            	actual  : <nil>(<nil>)
        	Test:       	TestCainiaoCallbackAdapter_BuildResponse
    cainiao_adapter_test.go:207: 
        	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/cainiao_adapter_test.go:207
        	Error:      	Not equal: 
        	            	expected: string("200")
        	            	actual  : <nil>(<nil>)
        	Test:       	TestCainiaoCallbackAdapter_BuildResponse
{"level":"info","ts":1754804694.613823,"caller":"database/query_optimizer.go:111","msg":"数据库连接池已优化","max_open_conns":200,"max_idle_conns":100,"conn_max_lifetime":1800}
2025-08-10T13:44:54.614+0800	DEBUG	service/smart_order_finder.go:114	🚀 执行优化UNION查询	{"identifier": "gk1752424403", "user_id": "d7e45ff4-cb3d-470c-9fbc-22114639d096"}
2025-08-10T13:44:54.614+0800	DEBUG	service/smart_order_finder.go:121	优化查询失败，回退到原始查询	{"error": "查询订单失败: unrecognized token: \":\""}
2025-08-10T13:44:54.614+0800	DEBUG	service/smart_order_finder.go:136	🔍 执行订单查询	{"identifier": "gk1752424403", "user_id": "d7e45ff4-cb3d-470c-9fbc-22114639d096"}
2025-08-10T13:44:54.614+0800	DEBUG	service/smart_order_finder.go:156	客户订单号查询数据库成功	{"customer_order_no": "gk1752424403", "found_user_id": "d7e45ff4-cb3d-470c-9fbc-22114639d096", "request_user_id": "d7e45ff4-cb3d-470c-9fbc-22114639d096"}
2025-08-10T13:44:54.614+0800	INFO	service/smart_order_finder.go:162	✅ 客户订单号查询成功	{"customer_order_no": "gk1752424403"}
--- FAIL: TestPlatformOrderNoExtraction (0.00s)
    --- FAIL: TestPlatformOrderNoExtraction/通过供应商订单号查找-应返回平台订单号 (0.00s)
        platform_order_extraction_test.go:130: 
            	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/platform_order_extraction_test.go:130
            	Error:      	Received unexpected error:
            	            	查询订单记录失败: unrecognized token: ":"
            	Test:       	TestPlatformOrderNoExtraction/通过供应商订单号查找-应返回平台订单号
        platform_order_extraction_test.go:131: 
            	Error Trace:	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/platform_order_extraction_test.go:131
            	Error:      	Expected value not to be nil.
            	Test:       	TestPlatformOrderNoExtraction/通过供应商订单号查找-应返回平台订单号
panic: runtime error: invalid memory address or nil pointer dereference [recovered]
	panic: runtime error: invalid memory address or nil pointer dereference
[signal SIGSEGV: segmentation violation code=0x1 addr=0x8 pc=0x105e73862]

goroutine 90 [running]:
testing.tRunner.func1.2({0x106314960, 0x106c8ce60})
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1734 +0x21c
testing.tRunner.func1()
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1737 +0x35e
panic({0x106314960?, 0x106c8ce60?})
	/usr/local/Cellar/go/1.24.4/libexec/src/runtime/panic.go:792 +0x132
github.com/your-org/go-kuaidi/internal/service/callback.TestPlatformOrderNoExtraction.func3(0xc0003dca80)
	/Users/<USER>/Desktop/go-kuaidi-*********/internal/service/callback/platform_order_extraction_test.go:134 +0x82
testing.tRunner(0xc0003dca80, 0xc0001199c0)
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1792 +0xf4
created by testing.(*T).Run in goroutine 84
	/usr/local/Cellar/go/1.24.4/libexec/src/testing/testing.go:1851 +0x413
FAIL	github.com/your-org/go-kuaidi/internal/service/callback	0.025s
	github.com/your-org/go-kuaidi/internal/user		coverage: 0.0% of statements
ok  	github.com/your-org/go-kuaidi/internal/util	0.034s	coverage: 12.5% of statements
	github.com/your-org/go-kuaidi/internal/utils		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/validator		coverage: 0.0% of statements
	github.com/your-org/go-kuaidi/internal/workorder		coverage: 0.0% of statements
FAIL	github.com/your-org/go-kuaidi/scripts [build failed]
ok  	github.com/your-org/go-kuaidi/test/error_handling	0.018s	coverage: [no statements]
FAIL
