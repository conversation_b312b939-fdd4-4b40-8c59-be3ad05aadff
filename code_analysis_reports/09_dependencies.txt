模块依赖分析:
github.com/your-org/go-kuaidi: [context database/sql encoding/json fmt github.com/lib/pq log net/http strings time]
github.com/your-org/go-kuaidi/api/handler: [context crypto/aes crypto/cipher database/sql encoding/base64 encoding/json errors fmt github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/google/uuid github.com/shopspring/decimal github.com/your-org/go-kuaidi/api/middleware github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/benchmark github.com/your-org/go-kuaidi/internal/errors github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/memory github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/pool github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/security github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/service/callback github.com/your-org/go-kuaidi/internal/user github.com/your-org/go-kuaidi/internal/util go.uber.org/zap io math net/http net/url regexp runtime slices sort strconv strings sync time]
github.com/your-org/go-kuaidi/api/middleware: [bytes compress/gzip context encoding/json fmt github.com/gin-contrib/cors github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/google/uuid github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/errors github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/security github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/user github.com/your-org/go-kuaidi/internal/util go.uber.org/zap io net/http os regexp runtime/debug sort strconv strings sync time]
github.com/your-org/go-kuaidi/api/router: [bytes encoding/json github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/swaggo/files github.com/swaggo/gin-swagger github.com/your-org/go-kuaidi/api/handler github.com/your-org/go-kuaidi/api/middleware github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/api github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/handler github.com/your-org/go-kuaidi/internal/middleware github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/security github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/user go.uber.org/zap gorm.io/gorm io net/http strings time]
github.com/your-org/go-kuaidi/api/routes: [github.com/gin-gonic/gin github.com/your-org/go-kuaidi/api/handler github.com/your-org/go-kuaidi/api/middleware github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/handler github.com/your-org/go-kuaidi/internal/middleware github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service/callback go.uber.org/zap time]
github.com/your-org/go-kuaidi/cmd: [context database/sql fmt github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/lib/pq github.com/your-org/go-kuaidi/api/handler github.com/your-org/go-kuaidi/api/middleware github.com/your-org/go-kuaidi/api/router github.com/your-org/go-kuaidi/docs github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/api github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/handler github.com/your-org/go-kuaidi/internal/logging github.com/your-org/go-kuaidi/internal/memory github.com/your-org/go-kuaidi/internal/middleware github.com/your-org/go-kuaidi/internal/pool github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/security github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/service/callback github.com/your-org/go-kuaidi/internal/user github.com/your-org/go-kuaidi/internal/util github.com/your-org/go-kuaidi/internal/workorder go.uber.org/zap gorm.io/driver/postgres gorm.io/gorm gorm.io/gorm/logger log net/http net/http/pprof os os/signal syscall time]
github.com/your-org/go-kuaidi/cmd/apiclient: [bytes crypto/hmac crypto/sha256 encoding/base64 encoding/json flag fmt io math/rand net/http os sort strings time]
github.com/your-org/go-kuaidi/cmd/billing-checker: [context flag fmt github.com/your-org/go-kuaidi/internal/service go.uber.org/zap log os time]
github.com/your-org/go-kuaidi/cmd/config-tool: [flag fmt github.com/your-org/go-kuaidi/internal/config go.uber.org/zap os]
github.com/your-org/go-kuaidi/cmd/demo: [fmt github.com/gin-contrib/cors github.com/gin-gonic/gin log net/http strconv time]
github.com/your-org/go-kuaidi/cmd/genkeys: [flag fmt github.com/your-org/go-kuaidi/internal/auth log os path/filepath]
github.com/your-org/go-kuaidi/cmd/init-routes: [bufio context database/sql github.com/lib/pq log os strings]
github.com/your-org/go-kuaidi/cmd/init-weight-cache: [context database/sql fmt github.com/lib/pq log os time]
github.com/your-org/go-kuaidi/cmd/manageclient: [database/sql flag fmt github.com/lib/pq github.com/your-org/go-kuaidi/internal/auth log os strings]
github.com/your-org/go-kuaidi/cmd/migrate: [context database/sql flag fmt github.com/lib/pq github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service go.uber.org/zap os time]
github.com/your-org/go-kuaidi/cmd/nonce-generator: [crypto/rand crypto/sha256 encoding/hex flag fmt log time]
github.com/your-org/go-kuaidi/cmd/security-check: [bufio flag fmt github.com/your-org/go-kuaidi/internal/security go.uber.org/zap os strings]
github.com/your-org/go-kuaidi/cmd/simple_demo: [context fmt github.com/go-redis/redis/v8 github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/service go.uber.org/zap os os/signal syscall time]
github.com/your-org/go-kuaidi/cmd/standalone_demo: [context fmt github.com/go-redis/redis/v8 github.com/shopspring/decimal os os/signal sync syscall time]
github.com/your-org/go-kuaidi/cmd/stress_test: [context fmt github.com/go-redis/redis/v8 github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/service go.uber.org/zap math/rand os os/signal strings sync sync/atomic syscall time]
github.com/your-org/go-kuaidi/cmd/test-kuaidiniao-adapter: [context fmt github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/model log time]
github.com/your-org/go-kuaidi/cmd/workorder: [context database/sql flag fmt github.com/gin-gonic/gin github.com/lib/pq github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/repository go.uber.org/zap go.uber.org/zap/zapcore log net/http os os/signal syscall time]
github.com/your-org/go-kuaidi/docs: [github.com/swaggo/swag]
github.com/your-org/go-kuaidi/internal/adapter: [context crypto/md5 encoding/base64 encoding/hex encoding/json encoding/xml fmt github.com/go-playground/validator/v10 github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/util go.uber.org/zap golang.org/x/time/rate io math net/http net/url sort strconv strings sync time]
github.com/your-org/go-kuaidi/internal/adapter/workorder: [bytes context crypto/md5 encoding/base64 encoding/json fmt github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/util go.uber.org/zap io math mime/multipart net/http net/url strconv strings time]
github.com/your-org/go-kuaidi/internal/api: [github.com/gin-gonic/gin github.com/your-org/go-kuaidi/internal/user net/http]
github.com/your-org/go-kuaidi/internal/auth: [context crypto/rand crypto/rsa crypto/x509 database/sql encoding/pem errors fmt github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/golang-jwt/jwt/v4 github.com/google/uuid github.com/lib/pq github.com/your-org/go-kuaidi/internal/security go.uber.org/zap golang.org/x/crypto/bcrypt net/http os path/filepath strings time]
github.com/your-org/go-kuaidi/internal/benchmark: [context fmt github.com/your-org/go-kuaidi/internal/memory github.com/your-org/go-kuaidi/internal/util go.uber.org/zap runtime sync sync/atomic time]
github.com/your-org/go-kuaidi/internal/cache: [context crypto/md5 encoding/json fmt github.com/go-redis/redis/v8 go.uber.org/zap time]
github.com/your-org/go-kuaidi/internal/concurrent: [context fmt github.com/your-org/go-kuaidi/internal/pool go.uber.org/zap golang.org/x/time/rate sync time]
github.com/your-org/go-kuaidi/internal/config: [database/sql encoding/json fmt github.com/go-redis/redis/v8 github.com/lib/pq github.com/shopspring/decimal github.com/spf13/viper go.uber.org/zap gopkg.in/yaml.v3 net/url os path/filepath regexp strconv strings sync time]
github.com/your-org/go-kuaidi/internal/constants: []
github.com/your-org/go-kuaidi/internal/consumer: [context database/sql encoding/json fmt github.com/go-redis/redis/v8 github.com/your-org/go-kuaidi/internal/interfaces go.uber.org/zap time]
github.com/your-org/go-kuaidi/internal/database: [context database/sql encoding/json fmt github.com/lib/pq github.com/prometheus/client_golang/prometheus go.uber.org/zap strings sync time]
github.com/your-org/go-kuaidi/internal/errors: [fmt net/http time]
github.com/your-org/go-kuaidi/internal/express: [context database/sql encoding/json errors fmt github.com/google/uuid go.uber.org/zap net/url regexp strconv strings sync time]
github.com/your-org/go-kuaidi/internal/handler: [context encoding/json fmt github.com/gin-gonic/gin github.com/google/uuid github.com/your-org/go-kuaidi/internal/middleware github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/service/callback github.com/your-org/go-kuaidi/internal/util go.uber.org/zap net/http strconv time]
github.com/your-org/go-kuaidi/internal/http: [context crypto/tls go.uber.org/zap net net/http sync time]
github.com/your-org/go-kuaidi/internal/infrastructure/database: [context database/sql]
github.com/your-org/go-kuaidi/internal/interfaces: [context github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/model]
github.com/your-org/go-kuaidi/internal/logging: [fmt github.com/your-org/go-kuaidi/internal/config go.uber.org/zap go.uber.org/zap/zapcore gopkg.in/natefinch/lumberjack.v2 os path/filepath strings time]
github.com/your-org/go-kuaidi/internal/memory: [go.uber.org/zap runtime runtime/debug strings sync time]
github.com/your-org/go-kuaidi/internal/middleware: [github.com/gin-gonic/gin github.com/your-org/go-kuaidi/internal/user net/http]
github.com/your-org/go-kuaidi/internal/model: [database/sql/driver encoding/json errors fmt github.com/google/uuid github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/util math/rand strconv strings sync time]
github.com/your-org/go-kuaidi/internal/pool: [context runtime sync time]
github.com/your-org/go-kuaidi/internal/region: [encoding/json fmt github.com/your-org/go-kuaidi/internal/config go.uber.org/zap os path/filepath sync]
github.com/your-org/go-kuaidi/internal/repository: [context database/sql encoding/base64 encoding/json errors fmt github.com/google/uuid github.com/lib/pq github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/database github.com/your-org/go-kuaidi/internal/infrastructure/database github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/util go.uber.org/zap math/rand strconv strings sync time]
github.com/your-org/go-kuaidi/internal/security: [context crypto/aes crypto/cipher crypto/hmac crypto/rand crypto/sha256 encoding/base64 encoding/hex encoding/json errors fmt github.com/go-redis/redis/v8 github.com/google/uuid github.com/your-org/go-kuaidi/internal/util go.uber.org/zap gorm.io/gorm io math/rand net/url os regexp sort strconv strings sync time]
github.com/your-org/go-kuaidi/internal/service: [bytes context crypto/hmac crypto/md5 crypto/rand crypto/sha256 crypto/tls database/sql encoding/hex encoding/json errors fmt github.com/gin-gonic/gin github.com/go-redis/redis/v8 github.com/google/uuid github.com/lib/pq github.com/prometheus/client_golang/prometheus github.com/prometheus/client_golang/prometheus/promauto github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/adapter github.com/your-org/go-kuaidi/internal/cache github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/constants github.com/your-org/go-kuaidi/internal/database github.com/your-org/go-kuaidi/internal/errors github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/infrastructure/database github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/pool github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/user github.com/your-org/go-kuaidi/internal/util github.com/your-org/go-kuaidi/internal/utils go.uber.org/zap gopkg.in/yaml.v2 gorm.io/driver/postgres gorm.io/gorm gorm.io/gorm/logger io log math math/big net net/http net/url os path/filepath regexp runtime sort strconv strings sync sync/atomic time]
github.com/your-org/go-kuaidi/internal/service/callback: [bytes context crypto/hmac crypto/md5 crypto/sha256 database/sql encoding/hex encoding/json errors fmt github.com/google/uuid github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/constants github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/interfaces github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service github.com/your-org/go-kuaidi/internal/util github.com/your-org/go-kuaidi/internal/utils go.uber.org/zap io math net net/http net/url strconv strings sync time]
github.com/your-org/go-kuaidi/internal/user: [context database/sql encoding/json errors fmt github.com/gin-gonic/gin github.com/google/uuid github.com/your-org/go-kuaidi/internal/auth github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/errors go.uber.org/zap golang.org/x/crypto/bcrypt log net/http regexp slices strconv strings time unicode]
github.com/your-org/go-kuaidi/internal/util: [bytes encoding/json fmt github.com/gin-gonic/gin github.com/go-playground/validator/v10 github.com/json-iterator/go go.uber.org/zap net/http reflect regexp strings sync time unicode/utf8]
github.com/your-org/go-kuaidi/internal/utils: [fmt github.com/your-org/go-kuaidi/internal/express github.com/your-org/go-kuaidi/internal/model go.uber.org/zap math strconv strings]
github.com/your-org/go-kuaidi/internal/validator: [fmt net/mail regexp strings unicode unicode/utf8]
github.com/your-org/go-kuaidi/internal/workorder: [context database/sql fmt github.com/your-org/go-kuaidi/api/handler github.com/your-org/go-kuaidi/internal/adapter/workorder github.com/your-org/go-kuaidi/internal/config github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service go.uber.org/zap]
github.com/your-org/go-kuaidi/scripts: [context database/sql encoding/json fmt github.com/google/uuid github.com/lib/pq github.com/prometheus/client_golang/prometheus github.com/shopspring/decimal github.com/your-org/go-kuaidi/internal/model github.com/your-org/go-kuaidi/internal/repository github.com/your-org/go-kuaidi/internal/service/callback github.com/your-org/go-kuaidi/internal/util go.uber.org/zap log os time]
github.com/your-org/go-kuaidi/test/error_handling: []
