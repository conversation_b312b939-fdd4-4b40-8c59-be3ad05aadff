未使用函数检查:
-: # github.com/your-org/go-kuaidi
./test_mapping_service.go:13:6: main redeclared in this block
	./test_express_disable_fix.go:17:6: other declaration of main
./test_express_disable_fix.go:4:2: "context" imported and not used
./test_express_disable_fix.go:38:14: invalid operation: "=" * 60 (mismatched types untyped string and untyped int) (compile)
-: # github.com/your-org/go-kuaidi/api/handler [github.com/your-org/go-kuaidi/api/handler.test]
api/handler/order_handler_batch_test.go:4:2: "bytes" imported and not used
api/handler/order_handler_batch_test.go:9:2: "net/url" imported and not used
api/handler/order_handler_batch_test.go:43:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:109:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:162:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
api/handler/order_handler_batch_test.go:206:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal (compile)
-: # github.com/your-org/go-kuaidi/api/routes
api/routes/admin_raw_callback_routes.go:15:39: undefined: middleware.AdminAuthMiddleware (compile)
-: # github.com/your-org/go-kuaidi/scripts
scripts/test_admin_deposit_limit.go:130:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_check_integration.go:138:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_fix.go:122:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_service_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_service_fix.go:25:6: MockBalanceService redeclared in this block
	scripts/test_balance_fix.go:25:6: other declaration of MockBalanceService
scripts/test_balance_service_fix.go:30:6: NewMockBalanceService redeclared in this block
	scripts/test_balance_fix.go:30:6: other declaration of NewMockBalanceService
scripts/test_balance_service_fix.go:114:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:14:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_prometheus_fix.go:157:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:157:6: too many errors (compile)
-: # github.com/your-org/go-kuaidi/scripts [github.com/your-org/go-kuaidi/scripts.test]
scripts/test_admin_deposit_limit.go:130:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_check_integration.go:138:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_fix.go:122:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_balance_service_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_service_fix.go:25:6: MockBalanceService redeclared in this block
	scripts/test_balance_fix.go:25:6: other declaration of MockBalanceService
scripts/test_balance_service_fix.go:30:6: NewMockBalanceService redeclared in this block
	scripts/test_balance_fix.go:30:6: other declaration of NewMockBalanceService
scripts/test_balance_service_fix.go:114:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:14:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_prometheus_fix.go:157:6: main redeclared in this block
	scripts/retry_callback.go:21:6: other declaration of main
scripts/test_prometheus_fix.go:157:6: too many errors (compile)
internal/service/order_cancellation_service.go:240:36: func (*OrderCancellationService).handleKuaidiniaoActiveCancellationVerification is unused (U1000)
internal/service/balance_service_test.go:13:2: package go-kuaidi/internal/model is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/model) (compile)
internal/service/balance_service_test.go:14:2: package go-kuaidi/internal/repository is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/repository) (compile)
internal/service/balance_service_test.go:15:2: package go-kuaidi/internal/util is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/util) (compile)
internal/service/order_balance_integration_test.go:13:2: package go-kuaidi/internal/errors is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/errors) (compile)
