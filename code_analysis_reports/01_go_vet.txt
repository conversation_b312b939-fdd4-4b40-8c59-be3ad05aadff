go vet 检查结果:
internal/service/order_balance_integration_test.go:13:2: package go-kuaidi/internal/errors is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/errors)
# github.com/your-org/go-kuaidi
# [github.com/your-org/go-kuaidi]
vet: ./test_mapping_service.go:13:6: main redeclared in this block
# github.com/your-org/go-kuaidi/scripts
# [github.com/your-org/go-kuaidi/scripts]
vet: scripts/test_admin_deposit_limit.go:130:6: main redeclared in this block
# github.com/your-org/go-kuaidi/api/handler
# [github.com/your-org/go-kuaidi/api/handler]
vet: api/handler/order_handler_batch_test.go:43:18: cannot use mockService (variable of type *MockOrderService) as *service.OrderService value in struct literal
# github.com/your-org/go-kuaidi/api/routes
vet: api/routes/admin_raw_callback_routes.go:15:39: undefined: middleware.AdminAuthMiddleware
