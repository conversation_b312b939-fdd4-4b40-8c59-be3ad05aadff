package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	_ "github.com/lib/pq"
)

// 测试快递公司禁用功能修复效果
func main() {
	// 数据库连接
	db, err := sql.Open("postgres", "*************************************************/go_kuaidi")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	// 测试用例
	testCases := []struct {
		name        string
		provider    string
		expressCode string
		description string
	}{
		{"快递鸟-申通", "kuaidiniao", "STO", "测试快递鸟供应商对申通快递的禁用响应"},
		{"菜鸟-申通", "cainiao", "STO", "测试菜鸟供应商对申通快递的禁用响应"},
		{"快递100-申通", "kuaidi100", "STO", "测试快递100供应商对申通快递的禁用响应（对照组）"},
	}

	fmt.Println("🔍 开始测试快递公司禁用功能修复效果")
	fmt.Println("=" * 60)

	// 1. 先测试申通快递启用状态下的查价
	fmt.Println("\n📋 第一阶段：申通快递启用状态测试")
	for _, tc := range testCases {
		fmt.Printf("\n🔍 测试 %s\n", tc.description)
		result := testPriceQuery(tc.provider, tc.expressCode)
		fmt.Printf("结果: %s\n", result)
	}

	// 2. 禁用申通快递
	fmt.Println("\n🚫 禁用申通快递...")
	_, err = db.Exec("UPDATE express_companies SET is_active = false WHERE code = 'STO'")
	if err != nil {
		log.Fatal("禁用申通快递失败:", err)
	}
	fmt.Println("✅ 申通快递已禁用")

	// 等待缓存刷新
	fmt.Println("⏳ 等待缓存刷新...")
	time.Sleep(3 * time.Second)

	// 3. 测试申通快递禁用状态下的查价
	fmt.Println("\n📋 第二阶段：申通快递禁用状态测试")
	for _, tc := range testCases {
		fmt.Printf("\n🔍 测试 %s\n", tc.description)
		result := testPriceQuery(tc.provider, tc.expressCode)
		fmt.Printf("结果: %s\n", result)
	}

	// 4. 恢复申通快递启用状态
	fmt.Println("\n🔄 恢复申通快递启用状态...")
	_, err = db.Exec("UPDATE express_companies SET is_active = true WHERE code = 'STO'")
	if err != nil {
		log.Fatal("恢复申通快递失败:", err)
	}
	fmt.Println("✅ 申通快递已恢复启用")

	fmt.Println("\n🎯 测试完成！")
}

// 测试价格查询
func testPriceQuery(provider, expressCode string) string {
	// 构建查价请求
	requestBody := map[string]interface{}{
		"provider":      provider,
		"express_code":  expressCode,
		"from_province": "广东省",
		"from_city":     "深圳市",
		"from_district": "南山区",
		"to_province":   "北京市",
		"to_city":       "北京市",
		"to_district":   "朝阳区",
		"weight":        1.0,
		"length":        10.0,
		"width":         10.0,
		"height":        10.0,
	}

	jsonData, _ := json.Marshal(requestBody)

	// 发送HTTP请求
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("POST", "http://localhost:8080/api/v1/express/price", strings.NewReader(string(jsonData)))
	if err != nil {
		return fmt.Sprintf("❌ 请求创建失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-token") // 需要根据实际情况调整

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Sprintf("❌ 请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Sprintf("❌ 响应解析失败: %v", err)
	}

	// 分析结果
	if success, ok := response["success"].(bool); ok && success {
		if data, ok := response["data"].([]interface{}); ok {
			if len(data) > 0 {
				return "✅ 查价成功，返回价格数据"
			} else {
				return "🚫 查价成功，但无价格数据（正确行为：快递公司已禁用）"
			}
		}
	}

	if message, ok := response["message"].(string); ok {
		if strings.Contains(message, "禁用") || strings.Contains(message, "不支持") {
			return "🚫 查价被拒绝（正确行为：快递公司已禁用）"
		}
		return fmt.Sprintf("❌ 查价失败: %s", message)
	}

	return "❓ 未知响应格式"
}
