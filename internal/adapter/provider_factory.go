package adapter

import (
	"context"
	"fmt"

	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// ProviderAdapterFactory 供应商适配器工厂接口
type ProviderAdapterFactory interface {
	CreateKuaidi100Adapter(ctx context.Context) (ProviderAdapter, error)
	CreateYidaAdapter(ctx context.Context) (ProviderAdapter, error)
	CreateYuntongAdapter(ctx context.Context) (ProviderAdapter, error)
	CreateCainiaoAdapter(ctx context.Context) (ProviderAdapter, error)
	CreateKuaidiNiaoAdapter(ctx context.Context) (ProviderAdapter, error)
	IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
}

// DefaultProviderAdapterFactory 默认供应商适配器工厂
type DefaultProviderAdapterFactory struct {
	providerConfigService interface {
		IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
	}
	kuaidi100ConfigGetter interface {
		GetKuaidi100Config(ctx context.Context) (*Kuaidi100Config, error)
	}
	yidaConfigGetter interface {
		GetYidaConfig(ctx context.Context) (*YidaConfig, error)
	}
	yuntongConfigGetter interface {
		GetYuntongConfig(ctx context.Context) (*YuntongConfig, error)
	}
	cainiaoConfigGetter interface {
		GetCainiaoConfig(ctx context.Context) (*CainiaoConfig, error)
	}
	kuaidiniaoConfigGetter interface {
		GetKuaidiNiaoConfig(ctx context.Context) (*KuaidiNiaoConfig, error)
	}
	mappingService     express.ExpressMappingService
	expressCompanyRepo express.ExpressCompanyRepository
	logger             *zap.Logger
}

// NewDefaultProviderAdapterFactory 创建默认供应商适配器工厂
func NewDefaultProviderAdapterFactory(
	providerConfigService interface {
		GetKuaidi100Config(ctx context.Context) (*Kuaidi100Config, error)
		GetYidaConfig(ctx context.Context) (*YidaConfig, error)
		GetYuntongConfig(ctx context.Context) (*YuntongConfig, error)
		GetCainiaoConfig(ctx context.Context) (*CainiaoConfig, error)
		GetKuaidiNiaoConfig(ctx context.Context) (*KuaidiNiaoConfig, error)
		IsProviderEnabled(ctx context.Context, providerName string) (bool, error)
	},
	mappingService express.ExpressMappingService,
	expressCompanyRepo express.ExpressCompanyRepository,
	logger *zap.Logger,
) ProviderAdapterFactory {
	return &DefaultProviderAdapterFactory{
		providerConfigService: providerConfigService,
		kuaidi100ConfigGetter: providerConfigService,
		yidaConfigGetter:      providerConfigService,
		yuntongConfigGetter:   providerConfigService,
		cainiaoConfigGetter:   providerConfigService,
		kuaidiniaoConfigGetter: providerConfigService,
		mappingService:        mappingService,
		expressCompanyRepo:    expressCompanyRepo,
		logger:                logger,
	}
}

// CreateKuaidi100Adapter 创建快递100适配器
func (f *DefaultProviderAdapterFactory) CreateKuaidi100Adapter(ctx context.Context) (ProviderAdapter, error) {
	config, err := f.kuaidi100ConfigGetter.GetKuaidi100Config(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取快递100配置失败: %w", err)
	}

	adapter := NewKuaidi100Adapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("快递100适配器创建成功",
		zap.String("base_url", config.BaseURL),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// CreateKuaidi100AdapterForTracking 创建快递100适配器用于物流跟踪（不检查启用状态）
func (f *DefaultProviderAdapterFactory) CreateKuaidi100AdapterForTracking(ctx context.Context) (ProviderAdapter, error) {
	// 使用专门的配置获取方法，不检查启用状态
	configGetter, ok := f.kuaidi100ConfigGetter.(interface {
		GetKuaidi100ConfigForTracking(ctx context.Context) (*Kuaidi100Config, error)
	})
	if !ok {
		// 如果不支持跟踪专用方法，回退到普通方法
		return f.CreateKuaidi100Adapter(ctx)
	}

	config, err := configGetter.GetKuaidi100ConfigForTracking(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取快递100配置失败（物流跟踪）: %w", err)
	}

	adapter := NewKuaidi100Adapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("快递100适配器创建成功（物流跟踪）",
		zap.String("base_url", config.BaseURL),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// CreateYidaAdapter 创建易达适配器
func (f *DefaultProviderAdapterFactory) CreateYidaAdapter(ctx context.Context) (ProviderAdapter, error) {
	config, err := f.yidaConfigGetter.GetYidaConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取易达配置失败: %w", err)
	}

	adapter := NewYidaAdapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("易达适配器创建成功",
		zap.String("base_url", config.BaseURL),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// CreateYuntongAdapter 创建云通适配器
func (f *DefaultProviderAdapterFactory) CreateYuntongAdapter(ctx context.Context) (ProviderAdapter, error) {
	config, err := f.yuntongConfigGetter.GetYuntongConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取云通配置失败: %w", err)
	}

	adapter := NewYuntongAdapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("云通适配器创建成功",
		zap.String("base_url", config.BaseURL),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// CreateCainiaoAdapter 创建菜鸟裹裹适配器
func (f *DefaultProviderAdapterFactory) CreateCainiaoAdapter(ctx context.Context) (ProviderAdapter, error) {
	config, err := f.cainiaoConfigGetter.GetCainiaoConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取菜鸟裹裹配置失败: %w", err)
	}

	adapter := NewCainiaoAdapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("菜鸟裹裹适配器创建成功",
		zap.String("base_url", config.BaseURL),
		zap.String("environment", config.Environment),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// CreateKuaidiNiaoAdapter 创建快递鸟适配器
func (f *DefaultProviderAdapterFactory) CreateKuaidiNiaoAdapter(ctx context.Context) (ProviderAdapter, error) {
	config, err := f.kuaidiniaoConfigGetter.GetKuaidiNiaoConfig(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取快递鸟配置失败: %w", err)
	}

	adapter := NewKuaidiNiaoAdapter(*config, f.expressCompanyRepo)
	adapter.SetMappingService(f.mappingService)

	f.logger.Info("快递鸟适配器创建成功",
		zap.String("base_url", config.BaseURL),
		zap.String("environment", config.Environment),
		zap.Int("timeout", config.Timeout))

	return adapter, nil
}

// IsProviderEnabled 检查供应商是否启用
func (f *DefaultProviderAdapterFactory) IsProviderEnabled(ctx context.Context, providerName string) (bool, error) {
	return f.providerConfigService.IsProviderEnabled(ctx, providerName)
}

// CreateAdapter 根据供应商代码创建适配器
func (f *DefaultProviderAdapterFactory) CreateAdapter(ctx context.Context, providerCode string) (ProviderAdapter, error) {
	switch providerCode {
	case "kuaidi100":
		return f.CreateKuaidi100Adapter(ctx)
	case "yida":
		return f.CreateYidaAdapter(ctx)
	case "yuntong":
		return f.CreateYuntongAdapter(ctx)
	case "cainiao":
		return f.CreateCainiaoAdapter(ctx)
	case "kuaidiniao":
		return f.CreateKuaidiNiaoAdapter(ctx)
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", providerCode)
	}
}
