package service

import (
	"context"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"go-kuaidi/internal/model"
	"go-kuaidi/internal/repository"
	"go-kuaidi/internal/util"
)

// MockBalanceRepository 模拟余额仓库
type MockBalanceRepository struct {
	mock.Mock
}

func (m *MockBalanceRepository) GetBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserBalance), args.Error(1)
}

func (m *MockBalanceRepository) CreateBalance(ctx context.Context, balance *model.UserBalance) error {
	args := m.Called(ctx, balance)
	return args.Error(0)
}

func (m *MockBalanceRepository) UpdateBalanceWithVersion(ctx context.Context, userID string, newBalance, frozenBalance decimal.Decimal, version int64) error {
	args := m.Called(ctx, userID, newBalance, frozenBalance, version)
	return args.Error(0)
}

func (m *MockBalanceRepository) CreateTransaction(ctx context.Context, transaction *model.Transaction) error {
	args := m.Called(ctx, transaction)
	return args.Error(0)
}

func (m *MockBalanceRepository) GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.Transaction, error) {
	args := m.Called(ctx, orderNo)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Transaction), args.Error(1)
}

func (m *MockBalanceRepository) CreateDeposit(ctx context.Context, deposit *model.Deposit) error {
	args := m.Called(ctx, deposit)
	return args.Error(0)
}

func (m *MockBalanceRepository) UpdateDeposit(ctx context.Context, deposit *model.Deposit) error {
	args := m.Called(ctx, deposit)
	return args.Error(0)
}

func (m *MockBalanceRepository) GetDepositByID(ctx context.Context, id string) (*model.Deposit, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Deposit), args.Error(1)
}

func (m *MockBalanceRepository) ListTransactions(ctx context.Context, userID string, limit, offset int) ([]*model.Transaction, error) {
	args := m.Called(ctx, userID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Transaction), args.Error(1)
}

// TestCheckBalanceForOrder_Sufficient 测试余额充足的场景
func TestCheckBalanceForOrder_Sufficient(t *testing.T) {
	// 准备测试数据
	mockRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()
	
	service := &DefaultBalanceService{
		repository: mockRepo,
		logger:     logger,
	}

	userID := "test_user_001"
	requestAmount := decimal.NewFromFloat(100.0)
	currentBalance := decimal.NewFromFloat(200.0)

	// 设置mock期望
	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  currentBalance,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

	// 执行测试
	ctx := context.Background()
	result, err := service.CheckBalanceForOrder(ctx, userID, requestAmount)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, userID, result.UserID)
	assert.True(t, result.RequestedAmount.Equal(requestAmount))
	assert.True(t, result.CurrentBalance.Equal(currentBalance))
	assert.True(t, result.IsSufficient)
	assert.True(t, result.Shortage.IsZero())
	assert.Equal(t, "余额充足", result.Message)

	mockRepo.AssertExpectations(t)
}

// TestCheckBalanceForOrder_Insufficient 测试余额不足的场景
func TestCheckBalanceForOrder_Insufficient(t *testing.T) {
	// 准备测试数据
	mockRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()
	
	service := &DefaultBalanceService{
		repository: mockRepo,
		logger:     logger,
	}

	userID := "test_user_002"
	requestAmount := decimal.NewFromFloat(150.0)
	currentBalance := decimal.NewFromFloat(100.0)
	expectedShortage := decimal.NewFromFloat(50.0)

	// 设置mock期望
	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  currentBalance,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

	// 执行测试
	ctx := context.Background()
	result, err := service.CheckBalanceForOrder(ctx, userID, requestAmount)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, userID, result.UserID)
	assert.True(t, result.RequestedAmount.Equal(requestAmount))
	assert.True(t, result.CurrentBalance.Equal(currentBalance))
	assert.False(t, result.IsSufficient)
	assert.True(t, result.Shortage.Equal(expectedShortage))
	assert.Contains(t, result.Message, "余额不足")
	assert.Contains(t, result.Message, "50.00")

	mockRepo.AssertExpectations(t)
}

// TestCheckBalanceForOrder_ExactAmount 测试余额刚好等于订单金额的边界条件
func TestCheckBalanceForOrder_ExactAmount(t *testing.T) {
	// 准备测试数据
	mockRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()
	
	service := &DefaultBalanceService{
		repository: mockRepo,
		logger:     logger,
	}

	userID := "test_user_003"
	amount := decimal.NewFromFloat(100.0)

	// 设置mock期望
	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  amount,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

	// 执行测试
	ctx := context.Background()
	result, err := service.CheckBalanceForOrder(ctx, userID, amount)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.True(t, result.IsSufficient)
	assert.True(t, result.Shortage.IsZero())
	assert.Equal(t, "余额充足", result.Message)

	mockRepo.AssertExpectations(t)
}

// TestCheckBalanceForOrder_InvalidInput 测试无效输入参数
func TestCheckBalanceForOrder_InvalidInput(t *testing.T) {
	mockRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()
	
	service := &DefaultBalanceService{
		repository: mockRepo,
		logger:     logger,
	}

	ctx := context.Background()

	// 测试空用户ID
	result, err := service.CheckBalanceForOrder(ctx, "", decimal.NewFromFloat(100.0))
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, ErrInvalidUserID, err)

	// 测试无效金额
	result, err = service.CheckBalanceForOrder(ctx, "test_user", decimal.Zero)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, ErrInvalidAmount, err)

	// 测试负数金额
	result, err = service.CheckBalanceForOrder(ctx, "test_user", decimal.NewFromFloat(-10.0))
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Equal(t, ErrInvalidAmount, err)
}
