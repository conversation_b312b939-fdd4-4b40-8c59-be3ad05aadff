package service

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"go-kuaidi/internal/model"
	"go-kuaidi/internal/errors"
)

// MockOrderService 模拟订单服务
type MockOrderService struct {
	mock.Mock
}

func (m *MockOrderService) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.OrderResponse), args.Error(1)
}

// TestOrderCreation_WithBalanceCheck 测试带余额检查的订单创建流程
func TestOrderCreation_WithBalanceCheck(t *testing.T) {
	tests := []struct {
		name            string
		userBalance     decimal.Decimal
		orderAmount     decimal.Decimal
		expectSuccess   bool
		expectedError   string
	}{
		{
			name:          "余额充足_订单创建成功",
			userBalance:   decimal.NewFromFloat(200.0),
			orderAmount:   decimal.NewFromFloat(100.0),
			expectSuccess: true,
		},
		{
			name:          "余额不足_订单创建失败",
			userBalance:   decimal.NewFromFloat(50.0),
			orderAmount:   decimal.NewFromFloat(100.0),
			expectSuccess: false,
			expectedError: "余额不足",
		},
		{
			name:          "余额刚好_订单创建成功",
			userBalance:   decimal.NewFromFloat(100.0),
			orderAmount:   decimal.NewFromFloat(100.0),
			expectSuccess: true,
		},
		{
			name:          "余额略少_订单创建失败",
			userBalance:   decimal.NewFromFloat(99.99),
			orderAmount:   decimal.NewFromFloat(100.0),
			expectSuccess: false,
			expectedError: "余额不足",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备测试环境
			mockBalanceRepo := new(MockBalanceRepository)
			logger, _ := zap.NewDevelopment()

			balanceService := &DefaultBalanceService{
				repository: mockBalanceRepo,
				logger:     logger,
			}

			userID := "test_user_integration"
			
			// 设置余额mock
			mockBalance := &model.UserBalance{
				UserID:   userID,
				Balance:  tt.userBalance,
				Currency: "CNY",
				Status:   model.BalanceStatusActive,
				Version:  1,
			}
			mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

			// 执行余额检查
			ctx := context.Background()
			result, err := balanceService.CheckBalanceForOrder(ctx, userID, tt.orderAmount)

			if tt.expectSuccess {
				// 期望成功的情况
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.True(t, result.IsSufficient)
				assert.Equal(t, "余额充足", result.Message)
			} else {
				// 期望失败的情况
				assert.NoError(t, err) // 余额检查本身不应该报错
				assert.NotNil(t, result)
				assert.False(t, result.IsSufficient)
				assert.Contains(t, result.Message, tt.expectedError)
			}

			mockBalanceRepo.AssertExpectations(t)
		})
	}
}

// TestOrderCreation_ConcurrentBalanceCheck 测试并发余额检查
func TestOrderCreation_ConcurrentBalanceCheck(t *testing.T) {
	// 准备测试环境
	mockBalanceRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()

	balanceService := &DefaultBalanceService{
		repository: mockBalanceRepo,
		logger:     logger,
	}

	userID := "test_user_concurrent"
	userBalance := decimal.NewFromFloat(100.0)
	orderAmount := decimal.NewFromFloat(60.0)

	// 设置余额mock - 允许多次调用
	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  userBalance,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil).Times(5)

	// 并发执行余额检查
	ctx := context.Background()
	results := make(chan *model.BalanceCheckResult, 5)
	errors := make(chan error, 5)

	for i := 0; i < 5; i++ {
		go func() {
			result, err := balanceService.CheckBalanceForOrder(ctx, userID, orderAmount)
			results <- result
			errors <- err
		}()
	}

	// 收集结果
	successCount := 0
	for i := 0; i < 5; i++ {
		result := <-results
		err := <-errors

		assert.NoError(t, err)
		if result != nil && result.IsSufficient {
			successCount++
		}
	}

	// 验证所有检查都应该成功（因为余额充足）
	assert.Equal(t, 5, successCount)

	mockBalanceRepo.AssertExpectations(t)
}

// TestOrderCreation_BalanceCheckError 测试余额检查错误处理
func TestOrderCreation_BalanceCheckError(t *testing.T) {
	// 准备测试环境
	mockBalanceRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()

	balanceService := &DefaultBalanceService{
		repository: mockBalanceRepo,
		logger:     logger,
	}

	userID := "test_user_error"
	orderAmount := decimal.NewFromFloat(100.0)

	// 设置余额查询失败的mock
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户余额不存在"))

	// 执行余额检查
	ctx := context.Background()
	result, err := balanceService.CheckBalanceForOrder(ctx, userID, orderAmount)

	// 验证错误处理
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "获取余额失败")

	mockBalanceRepo.AssertExpectations(t)
}

// TestOrderCreation_PerformanceBenchmark 性能基准测试
func BenchmarkCheckBalanceForOrder(b *testing.B) {
	// 准备测试环境
	mockBalanceRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()

	balanceService := &DefaultBalanceService{
		repository: mockBalanceRepo,
		logger:     logger,
	}

	userID := "benchmark_user"
	orderAmount := decimal.NewFromFloat(100.0)
	userBalance := decimal.NewFromFloat(200.0)

	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  userBalance,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}

	// 设置mock期望 - 允许大量调用
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

	ctx := context.Background()

	// 重置计时器
	b.ResetTimer()

	// 执行基准测试
	for i := 0; i < b.N; i++ {
		result, err := balanceService.CheckBalanceForOrder(ctx, userID, orderAmount)
		if err != nil || !result.IsSufficient {
			b.Fatalf("余额检查失败: %v", err)
		}
	}
}

// TestOrderCreation_EdgeCases 测试边界情况
func TestOrderCreation_EdgeCases(t *testing.T) {
	mockBalanceRepo := new(MockBalanceRepository)
	logger, _ := zap.NewDevelopment()

	balanceService := &DefaultBalanceService{
		repository: mockBalanceRepo,
		logger:     logger,
	}

	ctx := context.Background()

	// 测试极小金额
	userID := "test_user_edge"
	smallAmount := decimal.NewFromFloat(0.01)
	userBalance := decimal.NewFromFloat(0.01)

	mockBalance := &model.UserBalance{
		UserID:   userID,
		Balance:  userBalance,
		Currency: "CNY",
		Status:   model.BalanceStatusActive,
		Version:  1,
	}
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)

	result, err := balanceService.CheckBalanceForOrder(ctx, userID, smallAmount)
	assert.NoError(t, err)
	assert.True(t, result.IsSufficient)

	mockBalanceRepo.AssertExpectations(t)
}
