package service

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// OptimizedPlatformOrderGenerator 优化版平台订单号生成器
// 🔥 企业级性能优化：实现序列号缓存、异步处理、批量预分配等优化策略
type OptimizedPlatformOrderGenerator struct {
	db     *sql.DB
	logger *zap.Logger
	config *OptimizedPlatformOrderConfig

	// 缓存相关
	cache      *SequenceCache
	cacheMutex sync.RWMutex

	// 异步处理
	asyncQueue chan *AsyncGenerationRequest
	workers    []*AsyncWorker

	// 性能监控
	metrics *GeneratorMetrics

	// 内部状态
	mu      sync.RWMutex
	started bool
}

// OptimizedPlatformOrderConfig 优化版配置
type OptimizedPlatformOrderConfig struct {
	// 基础配置
	Prefix        string        `json:"prefix"`         // 订单号前缀
	RetryAttempts int           `json:"retry_attempts"` // 重试次数
	RetryDelay    time.Duration `json:"retry_delay"`    // 重试延迟

	// 🔥 缓存优化配置
	CacheSize            int  `json:"cache_size"`             // 缓存大小
	PreAllocateSize      int  `json:"pre_allocate_size"`      // 预分配大小
	CacheRefillThreshold int  `json:"cache_refill_threshold"` // 缓存补充阈值
	EnableLocalCache     bool `json:"enable_local_cache"`     // 启用本地缓存

	// 🔥 异步处理配置
	EnableAsync    bool          `json:"enable_async"`     // 启用异步处理
	AsyncWorkers   int           `json:"async_workers"`    // 异步工作者数量
	AsyncQueueSize int           `json:"async_queue_size"` // 异步队列大小
	AsyncTimeout   time.Duration `json:"async_timeout"`    // 异步超时时间

	// 🔥 性能优化配置
	EnableBatchMode bool          `json:"enable_batch_mode"` // 启用批量模式
	BatchSize       int           `json:"batch_size"`        // 批量大小
	BatchInterval   time.Duration `json:"batch_interval"`    // 批量间隔

	// 监控配置
	EnableMetrics   bool          `json:"enable_metrics"`   // 启用指标监控
	MetricsInterval time.Duration `json:"metrics_interval"` // 指标收集间隔
}

// SequenceCache 序列号缓存
type SequenceCache struct {
	sequences map[string]*CachedSequence // 按日期缓存序列号
}

// CachedSequence 缓存的序列号
type CachedSequence struct {
	Date        string    `json:"date"`         // 日期
	Current     int64     `json:"current"`      // 当前序列号
	Max         int64     `json:"max"`          // 最大序列号
	LastRefill  time.Time `json:"last_refill"`  // 最后补充时间
	RefillCount int64     `json:"refill_count"` // 补充次数
}

// AsyncGenerationRequest 异步生成请求
type AsyncGenerationRequest struct {
	Context    context.Context
	ResultChan chan *AsyncGenerationResult
	Timestamp  time.Time
}

// AsyncGenerationResult 异步生成结果
type AsyncGenerationResult struct {
	OrderNo  string
	Error    error
	Duration time.Duration
}

// AsyncWorker 异步工作者
type AsyncWorker struct {
	ID        int
	Generator *OptimizedPlatformOrderGenerator
	Queue     chan *AsyncGenerationRequest
	StopChan  chan struct{}
	Logger    *zap.Logger
}

// GeneratorMetrics 生成器性能指标
type GeneratorMetrics struct {
	// 生成统计
	TotalGenerated   int64 `json:"total_generated"`   // 总生成数
	SuccessGenerated int64 `json:"success_generated"` // 成功生成数
	FailedGenerated  int64 `json:"failed_generated"`  // 失败生成数

	// 性能指标
	AvgGenerationTime time.Duration `json:"avg_generation_time"` // 平均生成时间
	MaxGenerationTime time.Duration `json:"max_generation_time"` // 最大生成时间
	MinGenerationTime time.Duration `json:"min_generation_time"` // 最小生成时间

	// 缓存统计
	CacheHits    int64 `json:"cache_hits"`    // 缓存命中数
	CacheMisses  int64 `json:"cache_misses"`  // 缓存未命中数
	CacheRefills int64 `json:"cache_refills"` // 缓存补充数

	// 异步统计
	AsyncRequests  int64 `json:"async_requests"`  // 异步请求数
	AsyncCompleted int64 `json:"async_completed"` // 异步完成数
	AsyncFailed    int64 `json:"async_failed"`    // 异步失败数

	// 时间戳
	LastUpdated time.Time `json:"last_updated"` // 最后更新时间

	// 互斥锁
	mutex sync.RWMutex
}

// NewOptimizedPlatformOrderGenerator 创建优化版平台订单号生成器
func NewOptimizedPlatformOrderGenerator(
	db *sql.DB,
	logger *zap.Logger,
	config *OptimizedPlatformOrderConfig,
) *OptimizedPlatformOrderGenerator {
	if config == nil {
		config = DefaultOptimizedPlatformOrderConfig()
	}

	generator := &OptimizedPlatformOrderGenerator{
		db:     db,
		logger: logger,
		config: config,
		cache: &SequenceCache{
			sequences: make(map[string]*CachedSequence),
		},
		metrics: &GeneratorMetrics{
			MinGenerationTime: time.Hour, // 初始化为一个大值
		},
		started: false,
	}

	// 初始化异步队列
	if config.EnableAsync {
		generator.asyncQueue = make(chan *AsyncGenerationRequest, config.AsyncQueueSize)
		generator.initAsyncWorkers()
	}

	return generator
}

// DefaultOptimizedPlatformOrderConfig 默认优化配置
func DefaultOptimizedPlatformOrderConfig() *OptimizedPlatformOrderConfig {
	return &OptimizedPlatformOrderConfig{
		// 基础配置
		Prefix:        "GK",
		RetryAttempts: 3,
		RetryDelay:    100 * time.Millisecond,

		// 🔥 缓存优化配置
		CacheSize:            1000, // 缓存1000个序列号
		PreAllocateSize:      100,  // 预分配100个序列号
		CacheRefillThreshold: 200,  // 剩余200个时补充
		EnableLocalCache:     true,

		// 🔥 异步处理配置
		EnableAsync:    true,
		AsyncWorkers:   5,               // 5个异步工作者
		AsyncQueueSize: 1000,            // 队列大小1000
		AsyncTimeout:   5 * time.Second, // 5秒超时

		// 🔥 性能优化配置
		EnableBatchMode: true,
		BatchSize:       50,                     // 批量50个
		BatchInterval:   100 * time.Millisecond, // 100ms间隔

		// 监控配置
		EnableMetrics:   true,
		MetricsInterval: 1 * time.Minute,
	}
}

// Start 启动优化版生成器
func (g *OptimizedPlatformOrderGenerator) Start(ctx context.Context) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.started {
		return fmt.Errorf("优化版生成器已经启动")
	}

	g.logger.Info("🚀 启动优化版平台订单号生成器",
		zap.String("prefix", g.config.Prefix),
		zap.Int("cache_size", g.config.CacheSize),
		zap.Bool("enable_async", g.config.EnableAsync),
		zap.Int("async_workers", g.config.AsyncWorkers))

	// 预热缓存
	if g.config.EnableLocalCache {
		if err := g.warmupCache(ctx); err != nil {
			g.logger.Warn("缓存预热失败", zap.Error(err))
		}
	}

	// 启动异步工作者
	if g.config.EnableAsync {
		g.startAsyncWorkers(ctx)
	}

	// 启动指标收集
	if g.config.EnableMetrics {
		go g.startMetricsCollection(ctx)
	}

	g.started = true
	g.logger.Info("✅ 优化版平台订单号生成器启动成功")
	return nil
}

// Stop 停止优化版生成器
func (g *OptimizedPlatformOrderGenerator) Stop() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.started {
		return fmt.Errorf("优化版生成器未启动")
	}

	// 停止异步工作者
	if g.config.EnableAsync {
		g.stopAsyncWorkers()
	}

	g.started = false
	g.logger.Info("🛑 优化版平台订单号生成器已停止")
	return nil
}

// GeneratePlatformOrderNo 生成平台订单号（优化版）
func (g *OptimizedPlatformOrderGenerator) GeneratePlatformOrderNo(ctx context.Context) (string, error) {
	startTime := time.Now()

	// 如果启用异步模式，使用异步生成
	if g.config.EnableAsync {
		return g.generateAsync(ctx)
	}

	// 同步生成
	orderNo, err := g.generateSync(ctx)

	// 更新指标
	duration := time.Since(startTime)
	g.updateMetrics(err == nil, duration)

	return orderNo, err
}

// generateSync 同步生成订单号
func (g *OptimizedPlatformOrderGenerator) generateSync(ctx context.Context) (string, error) {
	// 尝试从缓存获取
	if g.config.EnableLocalCache {
		if orderNo, ok := g.getFromCache(); ok {
			g.updateCacheMetrics(true)
			return orderNo, nil
		}
		g.updateCacheMetrics(false)
	}

	// 缓存未命中，从数据库生成
	return g.generateFromDatabase(ctx)
}

// generateAsync 异步生成订单号
func (g *OptimizedPlatformOrderGenerator) generateAsync(ctx context.Context) (string, error) {
	// 创建异步请求
	req := &AsyncGenerationRequest{
		Context:    ctx,
		ResultChan: make(chan *AsyncGenerationResult, 1),
		Timestamp:  time.Now(),
	}

	// 发送到异步队列
	select {
	case g.asyncQueue <- req:
		g.metrics.mutex.Lock()
		g.metrics.AsyncRequests++
		g.metrics.mutex.Unlock()
	case <-time.After(g.config.AsyncTimeout):
		return "", fmt.Errorf("异步队列已满，超时")
	}

	// 等待结果
	select {
	case result := <-req.ResultChan:
		if result.Error != nil {
			g.metrics.mutex.Lock()
			g.metrics.AsyncFailed++
			g.metrics.mutex.Unlock()
			return "", result.Error
		}

		g.metrics.mutex.Lock()
		g.metrics.AsyncCompleted++
		g.metrics.mutex.Unlock()

		g.updateMetrics(true, result.Duration)
		return result.OrderNo, nil
	case <-time.After(g.config.AsyncTimeout):
		return "", fmt.Errorf("异步生成超时")
	case <-ctx.Done():
		return "", ctx.Err()
	}
}

// getFromCache 从缓存获取序列号
func (g *OptimizedPlatformOrderGenerator) getFromCache() (string, bool) {
	g.cacheMutex.RLock()
	defer g.cacheMutex.RUnlock()

	dateStr := time.Now().Format("20060102")
	cached, exists := g.cache.sequences[dateStr]
	if !exists || cached.Current >= cached.Max {
		return "", false
	}

	// 原子递增
	g.cacheMutex.RUnlock()
	g.cacheMutex.Lock()
	cached.Current++
	current := cached.Current
	g.cacheMutex.Unlock()
	g.cacheMutex.RLock()

	// 检查是否需要补充缓存
	if cached.Max-current <= int64(g.config.CacheRefillThreshold) {
		go g.refillCache(dateStr)
	}

	orderNo := fmt.Sprintf("%s%s%09d", g.config.Prefix, dateStr, current)
	return orderNo, true
}

// generateFromDatabase 从数据库生成序列号
func (g *OptimizedPlatformOrderGenerator) generateFromDatabase(ctx context.Context) (string, error) {
	dateStr := time.Now().Format("20060102")

	// 如果启用批量模式，批量获取序列号
	if g.config.EnableBatchMode {
		return g.generateBatch(ctx, dateStr)
	}

	// 单个生成
	return g.generateSingle(ctx, dateStr)
}

// generateSingle 单个生成序列号
func (g *OptimizedPlatformOrderGenerator) generateSingle(ctx context.Context, dateStr string) (string, error) {
	var sequenceNo int64

	query := `
		INSERT INTO platform_order_sequences (date, current_sequence, updated_at)
		VALUES ($1, 1, NOW())
		ON CONFLICT (date)
		DO UPDATE SET 
			current_sequence = platform_order_sequences.current_sequence + 1,
			updated_at = NOW()
		RETURNING current_sequence
	`

	err := g.db.QueryRowContext(ctx, query, dateStr).Scan(&sequenceNo)
	if err != nil {
		return "", fmt.Errorf("生成序列号失败: %w", err)
	}

	orderNo := fmt.Sprintf("%s%s%09d", g.config.Prefix, dateStr, sequenceNo)
	return orderNo, nil
}

// generateBatch 批量生成序列号
func (g *OptimizedPlatformOrderGenerator) generateBatch(ctx context.Context, dateStr string) (string, error) {
	// 批量获取序列号范围
	var startSeq, endSeq int64

	query := `
		INSERT INTO platform_order_sequences (date, current_sequence, updated_at)
		VALUES ($1, $2, NOW())
		ON CONFLICT (date)
		DO UPDATE SET 
			current_sequence = platform_order_sequences.current_sequence + $2,
			updated_at = NOW()
		RETURNING current_sequence - $2 + 1, current_sequence
	`

	err := g.db.QueryRowContext(ctx, query, dateStr, g.config.BatchSize).Scan(&startSeq, &endSeq)
	if err != nil {
		return "", fmt.Errorf("批量生成序列号失败: %w", err)
	}

	// 更新缓存
	if g.config.EnableLocalCache {
		g.updateCacheWithBatch(dateStr, startSeq, endSeq)
	}

	// 返回第一个序列号
	orderNo := fmt.Sprintf("%s%s%09d", g.config.Prefix, dateStr, startSeq)
	return orderNo, nil
}

// updateCacheWithBatch 批量更新缓存
func (g *OptimizedPlatformOrderGenerator) updateCacheWithBatch(dateStr string, startSeq, endSeq int64) {
	g.cacheMutex.Lock()
	defer g.cacheMutex.Unlock()

	cached, exists := g.cache.sequences[dateStr]
	if !exists {
		cached = &CachedSequence{
			Date: dateStr,
		}
		g.cache.sequences[dateStr] = cached
	}

	cached.Current = startSeq - 1 // 下次从startSeq开始
	cached.Max = endSeq
	cached.LastRefill = time.Now()
	cached.RefillCount++

	g.metrics.mutex.Lock()
	g.metrics.CacheRefills++
	g.metrics.mutex.Unlock()
}

// refillCache 补充缓存
func (g *OptimizedPlatformOrderGenerator) refillCache(dateStr string) {
	// 检查数据库连接
	if g.db == nil {
		g.logger.Debug("数据库连接为空，跳过缓存补充", zap.String("date", dateStr))
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 批量获取更多序列号
	var startSeq, endSeq int64

	query := `
		UPDATE platform_order_sequences
		SET current_sequence = current_sequence + $2, updated_at = NOW()
		WHERE date = $1
		RETURNING current_sequence - $2 + 1, current_sequence
	`

	err := g.db.QueryRowContext(ctx, query, dateStr, g.config.PreAllocateSize).Scan(&startSeq, &endSeq)
	if err != nil {
		g.logger.Error("补充缓存失败", zap.String("date", dateStr), zap.Error(err))
		return
	}

	// 更新缓存
	g.updateCacheWithBatch(dateStr, startSeq, endSeq)

	g.logger.Debug("缓存补充成功",
		zap.String("date", dateStr),
		zap.Int64("start_seq", startSeq),
		zap.Int64("end_seq", endSeq))
}

// warmupCache 预热缓存
func (g *OptimizedPlatformOrderGenerator) warmupCache(ctx context.Context) error {
	dateStr := time.Now().Format("20060102")

	g.logger.Info("开始缓存预热", zap.String("date", dateStr))

	// 预分配序列号
	_, err := g.generateBatch(ctx, dateStr)
	if err != nil {
		return fmt.Errorf("缓存预热失败: %w", err)
	}

	g.logger.Info("缓存预热完成", zap.String("date", dateStr))
	return nil
}

// initAsyncWorkers 初始化异步工作者
func (g *OptimizedPlatformOrderGenerator) initAsyncWorkers() {
	g.workers = make([]*AsyncWorker, g.config.AsyncWorkers)
	for i := 0; i < g.config.AsyncWorkers; i++ {
		g.workers[i] = &AsyncWorker{
			ID:        i,
			Generator: g,
			Queue:     g.asyncQueue,
			StopChan:  make(chan struct{}),
			Logger:    g.logger.With(zap.Int("worker_id", i)),
		}
	}
}

// startAsyncWorkers 启动异步工作者
func (g *OptimizedPlatformOrderGenerator) startAsyncWorkers(ctx context.Context) {
	for _, worker := range g.workers {
		go worker.Start(ctx)
	}
	g.logger.Info("异步工作者启动完成", zap.Int("worker_count", len(g.workers)))
}

// stopAsyncWorkers 停止异步工作者
func (g *OptimizedPlatformOrderGenerator) stopAsyncWorkers() {
	for _, worker := range g.workers {
		close(worker.StopChan)
	}
	g.logger.Info("异步工作者停止完成")
}

// Start 启动异步工作者
func (w *AsyncWorker) Start(ctx context.Context) {
	w.Logger.Info("异步工作者启动")

	for {
		select {
		case req := <-w.Queue:
			w.processRequest(req)
		case <-w.StopChan:
			w.Logger.Info("异步工作者停止")
			return
		case <-ctx.Done():
			w.Logger.Info("异步工作者上下文取消")
			return
		}
	}
}

// processRequest 处理异步请求
func (w *AsyncWorker) processRequest(req *AsyncGenerationRequest) {
	startTime := time.Now()

	orderNo, err := w.Generator.generateSync(req.Context)
	duration := time.Since(startTime)

	result := &AsyncGenerationResult{
		OrderNo:  orderNo,
		Error:    err,
		Duration: duration,
	}

	select {
	case req.ResultChan <- result:
		// 成功发送结果
	case <-time.After(1 * time.Second):
		w.Logger.Warn("发送异步结果超时")
	}
}

// updateMetrics 更新性能指标
func (g *OptimizedPlatformOrderGenerator) updateMetrics(success bool, duration time.Duration) {
	g.metrics.mutex.Lock()
	defer g.metrics.mutex.Unlock()

	g.metrics.TotalGenerated++
	if success {
		g.metrics.SuccessGenerated++
	} else {
		g.metrics.FailedGenerated++
	}

	// 更新时间指标
	if g.metrics.TotalGenerated == 1 {
		g.metrics.AvgGenerationTime = duration
		g.metrics.MaxGenerationTime = duration
		g.metrics.MinGenerationTime = duration
	} else {
		// 计算移动平均
		g.metrics.AvgGenerationTime = (g.metrics.AvgGenerationTime*time.Duration(g.metrics.TotalGenerated-1) + duration) / time.Duration(g.metrics.TotalGenerated)

		if duration > g.metrics.MaxGenerationTime {
			g.metrics.MaxGenerationTime = duration
		}
		if duration < g.metrics.MinGenerationTime {
			g.metrics.MinGenerationTime = duration
		}
	}

	g.metrics.LastUpdated = time.Now()
}

// updateCacheMetrics 更新缓存指标
func (g *OptimizedPlatformOrderGenerator) updateCacheMetrics(hit bool) {
	g.metrics.mutex.Lock()
	defer g.metrics.mutex.Unlock()

	if hit {
		g.metrics.CacheHits++
	} else {
		g.metrics.CacheMisses++
	}
}

// GetMetrics 获取性能指标
func (g *OptimizedPlatformOrderGenerator) GetMetrics() *GeneratorMetrics {
	g.metrics.mutex.RLock()
	defer g.metrics.mutex.RUnlock()

	// 返回指标副本
	metrics := *g.metrics
	return &metrics
}

// startMetricsCollection 启动指标收集
func (g *OptimizedPlatformOrderGenerator) startMetricsCollection(ctx context.Context) {
	ticker := time.NewTicker(g.config.MetricsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			g.logger.Info("指标收集服务停止")
			return
		case <-ticker.C:
			g.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (g *OptimizedPlatformOrderGenerator) collectMetrics() {
	metrics := g.GetMetrics()

	// 计算缓存命中率
	var cacheHitRate float64
	if metrics.CacheHits+metrics.CacheMisses > 0 {
		cacheHitRate = float64(metrics.CacheHits) / float64(metrics.CacheHits+metrics.CacheMisses) * 100
	}

	// 计算成功率
	var successRate float64
	if metrics.TotalGenerated > 0 {
		successRate = float64(metrics.SuccessGenerated) / float64(metrics.TotalGenerated) * 100
	}

	g.logger.Info("📊 优化版生成器性能指标",
		zap.Int64("total_generated", metrics.TotalGenerated),
		zap.Int64("success_generated", metrics.SuccessGenerated),
		zap.Float64("success_rate", successRate),
		zap.Duration("avg_generation_time", metrics.AvgGenerationTime),
		zap.Duration("max_generation_time", metrics.MaxGenerationTime),
		zap.Duration("min_generation_time", metrics.MinGenerationTime),
		zap.Int64("cache_hits", metrics.CacheHits),
		zap.Int64("cache_misses", metrics.CacheMisses),
		zap.Float64("cache_hit_rate", cacheHitRate),
		zap.Int64("async_requests", metrics.AsyncRequests),
		zap.Int64("async_completed", metrics.AsyncCompleted))
}

// GetCacheStatus 获取缓存状态
func (g *OptimizedPlatformOrderGenerator) GetCacheStatus() map[string]*CachedSequence {
	g.cacheMutex.RLock()
	defer g.cacheMutex.RUnlock()

	status := make(map[string]*CachedSequence)
	for date, cached := range g.cache.sequences {
		// 返回副本
		status[date] = &CachedSequence{
			Date:        cached.Date,
			Current:     cached.Current,
			Max:         cached.Max,
			LastRefill:  cached.LastRefill,
			RefillCount: cached.RefillCount,
		}
	}

	return status
}

// ClearCache 清理缓存
func (g *OptimizedPlatformOrderGenerator) ClearCache() {
	g.cacheMutex.Lock()
	defer g.cacheMutex.Unlock()

	g.cache.sequences = make(map[string]*CachedSequence)
	g.logger.Info("缓存已清理")
}
