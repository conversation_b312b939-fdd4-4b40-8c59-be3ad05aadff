package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// MockOrderService 模拟订单服务
type MockOrderService struct {
	mock.Mock
}

func (m *MockOrderService) GetOrderList(ctx context.Context, req *model.OrderListRequest) (*model.OrderListResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*model.OrderListResponse), args.Error(1)
}

// 实现其他必需的接口方法（简化版）
func (m *MockOrderService) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*model.OrderResponse), args.Error(1)
}

// TestOrderHandler_GetOrderList_BatchQuery 测试API层批量查询功能
func TestOrderHandler_GetOrderList_BatchQuery(t *testing.T) {
	gin.SetMode(gin.TestMode)
	logger, _ := zap.NewDevelopment()

	t.Run("批量查询参数解析 - 逗号分隔格式", func(t *testing.T) {
		mockService := new(MockOrderService)
		handler := &OrderHandler{
			orderService: mockService,
			logger:       logger,
		}

		// 准备期望的响应
		expectedResponse := &model.OrderListResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "成功",
			Data: &model.OrderListData{
				Items: []*model.OrderListItem{
					{ID: 1, TrackingNo: "SF1234567890"},
					{ID: 2, TrackingNo: "YT9876543210"},
				},
				Total:      2,
				Page:       1,
				PageSize:   20,
				TotalPages: 1,
				HasNext:    false,
				HasPrev:    false,
				BatchStats: &model.BatchQueryStats{
					TotalQueried: 3,
					FoundCount:   2,
					NotFound:     []string{"ZTO5555666677"},
					QueryTime:    "125.50ms",
				},
			},
		}

		// 设置模拟服务返回
		mockService.On("GetOrderList", mock.Anything, mock.MatchedBy(func(req *model.OrderListRequest) bool {
			return req.BatchMode &&
				len(req.TrackingNos) == 3 &&
				req.TrackingNos[0] == "SF1234567890" &&
				req.TrackingNos[1] == "YT9876543210" &&
				req.TrackingNos[2] == "ZTO5555666677"
		})).Return(expectedResponse, nil)

		// 创建测试请求
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 设置查询参数 - 逗号分隔格式
		c.Request = httptest.NewRequest("GET", "/api/v1/express/orders?tracking_nos=SF1234567890,YT9876543210,ZTO5555666677", nil)
		c.Set("userID", "user123")

		// 执行测试
		handler.GetOrderList(c)

		// 验证响应
		assert.Equal(t, http.StatusOK, w.Code)

		var response model.OrderListResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Success)
		assert.NotNil(t, response.Data.BatchStats)
		assert.Equal(t, 3, response.Data.BatchStats.TotalQueried)
		assert.Equal(t, 2, response.Data.BatchStats.FoundCount)

		mockService.AssertExpectations(t)
	})

	t.Run("批量查询参数解析 - 数组格式", func(t *testing.T) {
		mockService := new(MockOrderService)
		handler := &OrderHandler{
			orderService: mockService,
			logger:       logger,
		}

		expectedResponse := &model.OrderListResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "成功",
			Data: &model.OrderListData{
				Items:      []*model.OrderListItem{},
				Total:      0,
				Page:       1,
				PageSize:   20,
				TotalPages: 0,
				HasNext:    false,
				HasPrev:    false,
				BatchStats: &model.BatchQueryStats{
					TotalQueried: 2,
					FoundCount:   0,
					NotFound:     []string{"SF1111111111", "YT2222222222"},
					QueryTime:    "85.20ms",
				},
			},
		}

		mockService.On("GetOrderList", mock.Anything, mock.MatchedBy(func(req *model.OrderListRequest) bool {
			return req.BatchMode && len(req.TrackingNos) == 2
		})).Return(expectedResponse, nil)

		// 创建测试请求 - 数组格式
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/api/v1/express/orders?tracking_nos[]=SF1111111111&tracking_nos[]=YT2222222222", nil)
		c.Set("userID", "user123")

		handler.GetOrderList(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response model.OrderListResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Success)
		assert.NotNil(t, response.Data.BatchStats)
		assert.Equal(t, 0, response.Data.BatchStats.FoundCount)

		mockService.AssertExpectations(t)
	})

	t.Run("批量查询参数解析 - 混合格式", func(t *testing.T) {
		mockService := new(MockOrderService)
		handler := &OrderHandler{
			orderService: mockService,
			logger:       logger,
		}

		expectedResponse := &model.OrderListResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "成功",
			Data: &model.OrderListData{
				Items:      []*model.OrderListItem{},
				Total:      0,
				Page:       1,
				PageSize:   20,
				TotalPages: 0,
				HasNext:    false,
				HasPrev:    false,
			},
		}

		mockService.On("GetOrderList", mock.Anything, mock.MatchedBy(func(req *model.OrderListRequest) bool {
			// 验证去重后的结果
			return req.BatchMode && 
				len(req.TrackingNos) == 3 &&
				contains(req.TrackingNos, "SF1234567890") &&
				contains(req.TrackingNos, "YT9876543210") &&
				contains(req.TrackingNos, "ZTO5555666677")
		})).Return(expectedResponse, nil)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 混合格式：逗号分隔 + 数组格式，包含重复项
		c.Request = httptest.NewRequest("GET", "/api/v1/express/orders?tracking_nos=SF1234567890,YT9876543210&tracking_nos[]=ZTO5555666677&tracking_nos[]=SF1234567890", nil)
		c.Set("userID", "user123")

		handler.GetOrderList(c)

		assert.Equal(t, http.StatusOK, w.Code)
		mockService.AssertExpectations(t)
	})

	t.Run("非批量查询不返回统计信息", func(t *testing.T) {
		mockService := new(MockOrderService)
		handler := &OrderHandler{
			orderService: mockService,
			logger:       logger,
		}

		expectedResponse := &model.OrderListResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "成功",
			Data: &model.OrderListData{
				Items: []*model.OrderListItem{
					{ID: 1, TrackingNo: "SF1234567890"},
				},
				Total:      1,
				Page:       1,
				PageSize:   20,
				TotalPages: 1,
				HasNext:    false,
				HasPrev:    false,
				BatchStats: nil, // 非批量查询不返回统计信息
			},
		}

		mockService.On("GetOrderList", mock.Anything, mock.MatchedBy(func(req *model.OrderListRequest) bool {
			return !req.BatchMode && req.TrackingNo == "SF1234567890"
		})).Return(expectedResponse, nil)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 单个运单号查询
		c.Request = httptest.NewRequest("GET", "/api/v1/express/orders?tracking_no=SF1234567890", nil)
		c.Set("userID", "user123")

		handler.GetOrderList(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response model.OrderListResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.True(t, response.Success)
		assert.Nil(t, response.Data.BatchStats)

		mockService.AssertExpectations(t)
	})

	t.Run("用户身份验证失败", func(t *testing.T) {
		handler := &OrderHandler{
			logger: logger,
		}

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/api/v1/express/orders?tracking_nos=SF1234567890", nil)
		// 不设置userID，模拟身份验证失败

		handler.GetOrderList(c)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response model.ErrorResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.False(t, response.Success)
		assert.Equal(t, model.StatusUnauthorized, response.Code)
	})
}

// TestOrderHandler_parseBatchTrackingNos 测试批量运单号解析方法
func TestOrderHandler_parseBatchTrackingNos(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	handler := &OrderHandler{
		logger: logger,
	}

	t.Run("解析逗号分隔格式", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?tracking_nos=SF1234567890,YT9876543210,ZTO5555666677", nil)

		req := &model.OrderListRequest{}
		handler.parseBatchTrackingNos(c, req)

		assert.Equal(t, 3, len(req.TrackingNos))
		assert.Contains(t, req.TrackingNos, "SF1234567890")
		assert.Contains(t, req.TrackingNos, "YT9876543210")
		assert.Contains(t, req.TrackingNos, "ZTO5555666677")
	})

	t.Run("解析数组格式", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?tracking_nos[]=SF1234567890&tracking_nos[]=YT9876543210", nil)

		req := &model.OrderListRequest{}
		handler.parseBatchTrackingNos(c, req)

		assert.Equal(t, 2, len(req.TrackingNos))
		assert.Contains(t, req.TrackingNos, "SF1234567890")
		assert.Contains(t, req.TrackingNos, "YT9876543210")
	})

	t.Run("去重处理", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?tracking_nos=SF1234567890,YT9876543210&tracking_nos[]=SF1234567890", nil)

		req := &model.OrderListRequest{}
		handler.parseBatchTrackingNos(c, req)

		assert.Equal(t, 2, len(req.TrackingNos))
		assert.Contains(t, req.TrackingNos, "SF1234567890")
		assert.Contains(t, req.TrackingNos, "YT9876543210")
	})

	t.Run("处理空白字符", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?tracking_nos= SF1234567890 , YT9876543210 ,  ", nil)

		req := &model.OrderListRequest{}
		handler.parseBatchTrackingNos(c, req)

		assert.Equal(t, 2, len(req.TrackingNos))
		assert.Contains(t, req.TrackingNos, "SF1234567890")
		assert.Contains(t, req.TrackingNos, "YT9876543210")
	})
}

// 辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
